import initialState from './initialState';
import {
  ON_FETCH_MONEY_MULE_REPORT_MASTERS_LOADING,
  ON_FETCH_MONEY_MULE_REPORT_MASTERS_SUCCESS,
  ON_FETCH_MONEY_MULE_REPORT_MASTERS_FAILURE,
  ON_FETCH_MONEY_MULE_REPORT_DETAILS_LOADING,
  ON_FETCH_MONEY_MULE_REPORT_DETAILS_SUCCESS,
  ON_FETCH_MONEY_MULE_REPORT_DETAILS_FAILURE,
  ON_FETCH_MONEY_MULE_REPORT_LOGS_LOADING,
  ON_FETCH_MONEY_MULE_REPORT_LOGS_SUCCESS,
  ON_FETCH_MONEY_MULE_REPORT_LOGS_FAILURE,
  ON_FETCH_MONEY_MULE_REPORTS_LIST_LOADING,
  ON_<PERSON>ET<PERSON>_MONEY_MULE_REPORTS_LIST_SUCCESS,
  ON_FET<PERSON>_MONEY_MULE_REPORTS_LIST_FAILURE
} from 'constants/actionTypes';
import objectAssign from 'object-assign';

export default function moneyMuleReportReducer(state = initialState.moneyMuleReport, action) {
  switch (action.type) {
    case ON_FETCH_MONEY_MULE_REPORT_MASTERS_LOADING:
      return objectAssign({}, state, {
        masters: objectAssign({}, state.masters, {
          loader: true,
          error: false,
          errorMessage: ''
        })
      });

    case ON_FETCH_MONEY_MULE_REPORT_MASTERS_SUCCESS:
      return objectAssign({}, state, {
        masters: objectAssign({}, state.masters, {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        })
      });

    case ON_FETCH_MONEY_MULE_REPORT_MASTERS_FAILURE:
      return objectAssign({}, state, {
        masters: objectAssign({}, state.masters, {
          loader: false,
          error: true,
          errorMessage: action.response.message
        })
      });

    case ON_FETCH_MONEY_MULE_REPORT_DETAILS_LOADING:
      return objectAssign({}, state, {
        details: objectAssign({}, state.details, {
          loader: true,
          error: false,
          errorMessage: ''
        })
      });

    case ON_FETCH_MONEY_MULE_REPORT_DETAILS_SUCCESS:
      return objectAssign({}, state, {
        details: objectAssign({}, state.details, {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        })
      });

    case ON_FETCH_MONEY_MULE_REPORT_DETAILS_FAILURE:
      return objectAssign({}, state, {
        details: objectAssign({}, state.details, {
          loader: false,
          error: true,
          errorMessage: action.response.message
        })
      });

    case ON_FETCH_MONEY_MULE_REPORT_LOGS_LOADING:
      return objectAssign({}, state, {
        logs: objectAssign({}, state.logs, {
          loader: true,
          error: false,
          errorMessage: ''
        })
      });

    case ON_FETCH_MONEY_MULE_REPORT_LOGS_SUCCESS:
      return objectAssign({}, state, {
        logs: objectAssign({}, state.logs, {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        })
      });

    case ON_FETCH_MONEY_MULE_REPORT_LOGS_FAILURE:
      return objectAssign({}, state, {
        logs: objectAssign({}, state.logs, {
          loader: false,
          error: true,
          errorMessage: action.response.message
        })
      });

    case ON_FETCH_MONEY_MULE_REPORTS_LIST_LOADING:
      return objectAssign({}, state, {
        allReports: objectAssign({}, state.allReports, {
          loader: true,
          error: false,
          errorMessage: ''
        })
      });

    case ON_FETCH_MONEY_MULE_REPORTS_LIST_SUCCESS:
      return objectAssign({}, state, {
        allReports: objectAssign({}, state.allReports, {
          list: action.response.list,
          count: action.response.count,
          conf: action.response.conf,
          loader: false,
          error: false,
          errorMessage: ''
        })
      });

    case ON_FETCH_MONEY_MULE_REPORTS_LIST_FAILURE:
      return objectAssign({}, state, {
        allReports: objectAssign({}, state.allReports, {
          loader: false,
          error: true,
          errorMessage: action.response.message
        })
      });

    default:
      return state;
  }
}
