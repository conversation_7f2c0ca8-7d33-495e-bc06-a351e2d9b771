import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import Datetime from 'react-datetime';
import Moment from 'moment';
import { Button, FormGroup, Label, Input, TabPane } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/free-solid-svg-icons';

import Tabs from 'components/common/Tabs';
import ModalContainer from 'components/common/ModalContainer';
import { MAX_AMOUNT } from 'constants/applicationConstants';
import { isCooperative } from 'constants/publicKey';

function SpecializedListForm({
  listType,
  data,
  toggle,
  actions,
  categoryOptions,
  partnerIdOptions,
  currentPrefilterList,
  toggleFilterModal,
  isEditmode = false
}) {
  const [fileData, setFileData] = useState('');
  const [categoryName, setCategoryName] = useState('');
  const [identifier, setIdentifier] = useState('');
  const [amount, setAmount] = useState('');
  const [count, setCount] = useState('');
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [remark, setRemark] = useState('');
  const [partnerId, setPartnerId] = useState('');
  const [status, setStatus] = useState(false);
  const [bulkUploadLoading, setBulkUploadLoading] = useState(false);
  const [singleUploadLoading, setSingleUploadLoading] = useState(false);

  useEffect(() => {
    if (isEditmode) {
      setCategoryName(data?.categoryName);
      setIdentifier(data?.identifier);
      setAmount(data?.amount);
      setCount(data?.count);
      setStartDate(data?.startDate);
      setEndDate(data?.endDate);
      setRemark(data?.remark);
      setPartnerId(data?.partnerId);
      setStatus(data?.isActive === 1);
    } else {
      clearUpdatedValue();
    }
  }, [data]);

  const validEndDate = (current) => (startDate ? current.isSameOrAfter(Moment(startDate)) : true);
  const validStartDate = (current) => (endDate ? current.isSameOrBefore(endDate) : true);

  const clearUpdatedValue = () => {
    setCategoryName('');
    setIdentifier('');
    setAmount('');
    setCount('');
    setStartDate(null);
    setEndDate(null);
    setRemark('');
    setPartnerId('');
    setStatus(false);
  };

  const addSingleEntity = (e) => {
    e.preventDefault();
    setSingleUploadLoading(true);
    let formData = {
      categoryName,
      identifier,
      ...(amount && { amount: parseFloat(amount) }),
      ...(count && { count: parseInt(count) }),
      ...(startDate &&
        endDate && {
          startDate: Moment(startDate).format('YYYY-MM-DD HH:mm:ss'),
          endDate: Moment(endDate).format('YYYY-MM-DD HH:mm:ss')
        }),
      ...(remark && { remark }),
      ...(isCooperative && partnerId && { partnerId: +partnerId }),
      isActive: status ? 1 : 0
    };

    if (isEditmode) {
      actions.onUpdateSpecializedListItem(formData, currentPrefilterList, listType, isEditmode);
    } else {
      actions.onAddSingleItemToSpecializedList(formData, currentPrefilterList, listType);
    }

    setSingleUploadLoading(false);
    clearUpdatedValue();
  };

  const addInBulkEntity = (e) => {
    e.preventDefault();
    setBulkUploadLoading(true);
    let formData = {
      [currentPrefilterList.prefilterValue]: fileData
    };
    actions.onAddItemsInBulkToSpecializedList(formData, currentPrefilterList, listType);
    setBulkUploadLoading(false);
    setFileData('');
  };

  return (
    <ModalContainer
      size="md"
      theme={toggle.theme}
      isOpen={toggle.prefiltersListModal[listType]}
      toggle={() => toggleFilterModal('close')}
      header={`Add ${currentPrefilterList.prefilterName}`}>
      <Tabs tabNames={isEditmode ? ['Single'] : ['Single', 'Bulk']}>
        <TabPane tabId={0}>
          <form onSubmit={addSingleEntity}>
            <FormGroup>
              <Label for="categoryName">Category</Label>
              <Input
                type="select"
                id="categoryName"
                name="categoryName"
                value={categoryName}
                disabled={isEditmode}
                onChange={(event) => setCategoryName(event.target.value)}
                required>
                <option value="">-- select --</option>
                {categoryOptions}
              </Input>
            </FormGroup>
            <FormGroup>
              <Label for="identifier">Identifier</Label>
              <Input
                type="text"
                id="identifier"
                name="identifier"
                value={identifier}
                readOnly={isEditmode}
                onChange={(event) => setIdentifier(event.target.value)}
                required
              />
            </FormGroup>
            <FormGroup>
              <Label for="amount">Amount</Label>
              <Input
                type="number"
                id="amount"
                name="amount"
                value={amount}
                onChange={(event) => setAmount(event.target.value)}
                min={0}
                max={MAX_AMOUNT}
                step={0.01}
                pattern="^[0-9]*\.[0-9]{0,2}$"
              />
            </FormGroup>
            <FormGroup>
              <Label for="count">Count</Label>
              <Input
                type="number"
                id="count"
                name="count"
                value={count}
                onChange={(event) => setCount(event.target.value)}
              />
            </FormGroup>
            <FormGroup>
              <Label>Start Date</Label>
              <Datetime
                name="startDate"
                dateFormat="YYYY-MM-DD"
                timeFormat="HH:mm:ss"
                value={startDate}
                onChange={(dateObj) => setStartDate(dateObj._d)}
                renderInput={(props) => {
                  // eslint-disable-next-line react/prop-types
                  return <input {...props} value={startDate ? props.value : ''} />;
                }}
                isValidDate={validStartDate}
              />
            </FormGroup>
            <FormGroup>
              <Label>End Date</Label>
              <Datetime
                name="endDate"
                dateFormat="YYYY-MM-DD"
                timeFormat="HH:mm:ss"
                value={endDate}
                onChange={(dateObj) => setEndDate(dateObj._d)}
                renderInput={(props) => {
                  // eslint-disable-next-line react/prop-types
                  return <input {...props} value={endDate ? props.value : ''} />;
                }}
                isValidDate={validEndDate}
              />
            </FormGroup>
            <FormGroup>
              <Label for="remark">Remark</Label>
              <Input
                type="text"
                id="remark"
                name="remark"
                value={remark}
                onChange={(event) => setRemark(event.target.value)}
              />
            </FormGroup>
            {isCooperative && (
              <FormGroup>
                <Label>Select Partner ID</Label>
                <Input
                  type="select"
                  name="partnerId"
                  value={partnerId}
                  onChange={(e) => setPartnerId(e.target.value)}
                  disabled={isEditmode}
                  required>
                  <option value="">-- Select --</option>
                  {partnerIdOptions}
                </Input>
              </FormGroup>
            )}
            <FormGroup className="ms-4">
              <Label for="isActive">
                <Input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  value={status}
                  checked={status}
                  onChange={() => setStatus(!status)}
                />{' '}
                Activate
              </Label>
            </FormGroup>
            <FormGroup className="d-flex justify-content-end">
              <Button size="sm" type="submit" color="success" disabled={singleUploadLoading}>
                {singleUploadLoading ? (
                  <FontAwesomeIcon icon={faSpinner} className={'loader fa-spin'} />
                ) : (
                  'Save'
                )}
              </Button>
            </FormGroup>
          </form>
        </TabPane>
        <TabPane tabId={1}>
          <form onSubmit={addInBulkEntity}>
            <FormGroup>
              <Input
                name="fileUpload"
                accept=".csv"
                type="file"
                files={fileData}
                onChange={(event) => setFileData(event.target.files[0])}
                required
              />
            </FormGroup>
            <FormGroup className="d-flex justify-content-end">
              <Button size="sm" type="submit" color="success" disabled={toggle.uploadLoader}>
                {bulkUploadLoading ? (
                  <FontAwesomeIcon icon={faSpinner} className={'loader fa-spin'} />
                ) : (
                  'Upload'
                )}
              </Button>
            </FormGroup>
          </form>
        </TabPane>
      </Tabs>
    </ModalContainer>
  );
}

SpecializedListForm.propTypes = {
  isEditmode: PropTypes.bool,
  listType: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  toggle: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  categoryOptions: PropTypes.array.isRequired,
  partnerIdOptions: PropTypes.array.isRequired,
  currentPrefilterList: PropTypes.object.isRequired,
  toggleFilterModal: PropTypes.func.isRequired
};

export default SpecializedListForm;
