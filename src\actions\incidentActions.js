import client from 'utility/apiClient';
import {
  ON_FET<PERSON>_ALL_INCIDENTS_LOADING,
  ON_<PERSON>ET<PERSON>_ALL_INCIDENTS_SUCCESS,
  ON_FETCH_ALL_INCIDENTS_FAILURE,
  ON_FETCH_INCIDENT_BY_ID_LOADING,
  ON_FETCH_INCIDENT_BY_ID_SUCCESS,
  ON_FETCH_INCIDENT_BY_ID_FAILURE,
  ON_FETCH_CPIFR_MASTERS_LOADING,
  ON_FETCH_CPIFR_MASTERS_SUCCESS,
  ON_FETCH_CPIFR_MASTERS_FAILURE,
  ON_FETCH_CASE_CLOSED_CPIFR_LOADING,
  ON_FETCH_CASE_CLOSED_CPIFR_SUCCESS,
  ON_FETCH_CASE_CLOSED_CPIFR_FAILURE,
  ON_FETCH_CPIFR_REPORT_HISTORY_LOADING,
  ON_FETCH_CPIFR_REPORT_HISTORY_SUCCESS,
  ON_<PERSON>ET<PERSON>_CPIFR_REPORT_HISTORY_FAILURE,
  ON_C<PERSON>AR_SELECTED_INCIDENT,
  ON_FETCH_UNVERIFIED_CASES_LOADING,
  ON_FETCH_UNVERIFIED_CASES_SUCCESS,
  ON_FETCH_UNVERIFIED_CASES_FAILURE,
  ON_REMOVE_CASES_FROM_UNVERIFIED_LIST
} from 'constants/actionTypes';
import { onShowFailureAlert, onShowSuccessAlert, onShowWarningAlert } from 'actions/alertActions';
import { onToggleCPIFRFormModal, onToggleLoader } from './toggleActions';
import DownloadFile from 'js-file-download';
import { onUpdateCaseDetails } from './caseDetailsActions';

function createIncidents(formData) {
  return client({ method: 'POST', url: `casereview/cpifr/create/incident`, data: formData });
}

function onCreateIncidents(formData) {
  return function (dispatch, getState) {
    dispatch(onToggleLoader(true));
    return createIncidents(formData)
      .then(
        (success) => {
          dispatch(onFetchIncidentByIdSuccess(success));
          dispatch(onToggleCPIFRFormModal());
          const { conf } = getState().incidents.closedList;
          dispatch(onFetchClosedCasesCPIFR(conf));
        },
        (error) => onShowFailureAlert(error)
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchAllIncidents() {
  return client({ url: `casereview/cpifr/fetch/incidents/all` });
}

function onFetchAllIncidentsLoading() {
  return { type: ON_FETCH_ALL_INCIDENTS_LOADING };
}

function onFetchAllIncidentsSuccess(response) {
  return { type: ON_FETCH_ALL_INCIDENTS_SUCCESS, response };
}

function onFetchAllIncidentsFailure(response) {
  return { type: ON_FETCH_ALL_INCIDENTS_FAILURE, response };
}

function onFetchAllIncidents() {
  return function (dispatch) {
    dispatch(onFetchAllIncidentsLoading());
    return fetchAllIncidents().then(
      (success) => dispatch(onFetchAllIncidentsSuccess(success)),
      (error) => dispatch(onFetchAllIncidentsFailure(error))
    );
  };
}

function fetchIncidentById(incidentId) {
  return client({ url: `casereview/cpifr/fetch/incidents/${incidentId}` });
}

function onFetchIncidentByIdLoading() {
  return { type: ON_FETCH_INCIDENT_BY_ID_LOADING };
}

function onFetchIncidentByIdSuccess(response) {
  return { type: ON_FETCH_INCIDENT_BY_ID_SUCCESS, response };
}

function onFetchIncidentByIdFailure(response) {
  return { type: ON_FETCH_INCIDENT_BY_ID_FAILURE, response };
}

function onFetchIncidentById(incidentId) {
  return function (dispatch) {
    dispatch(onFetchIncidentByIdLoading());
    return fetchIncidentById(incidentId).then(
      (success) => dispatch(onFetchIncidentByIdSuccess(success)),
      (error) => dispatch(onFetchIncidentByIdFailure(error))
    );
  };
}

function submitCPFIRReport(formData) {
  return client({
    method: 'POST',
    url: `casereview/cpifr/against/incident/report`,
    data: formData,
    badRequestMessage: 'Unable to generate CPFIR Report. Please verify all input details.'
  });
}

function onSubmitCPFIRReportSuccess(response, formData, dispatcher) {
  if (response?.length > 0) {
    DownloadFile(response, (formData.incidents[0].internalId || formData.incidentId) + '.txt');
    dispatcher(onShowSuccessAlert({ message: 'CPFIR File has been downloaded successfully!' }));
  } else {
    dispatcher(
      onShowWarningAlert({ message: 'Something went wrong. Unable to generate CPFIR report' })
    );
  }
}

function onSubmitCPFIRReport(formData) {
  return function (dispatch, getState) {
    const selectedCase = getState().caseAssignment.selectedCase;
    dispatch(onToggleLoader(true));
    return submitCPFIRReport(formData)
      .then(
        (success) => {
          onSubmitCPFIRReportSuccess(success, formData, dispatch);
          if (selectedCase?.caseRefNo)
            dispatch(onUpdateCaseDetails(selectedCase?.caseRefNo, null, selectedCase?.channel));
          dispatch(onFetchAllIncidents());
          dispatch(onToggleCPIFRFormModal());
          dispatch(onClearSelectedIncident());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchCPIFRMasters() {
  return client({ url: `casereview/cpifr/fetch/masters` });
}

function onFetchCPIFRMastersLoading() {
  return { type: ON_FETCH_CPIFR_MASTERS_LOADING };
}

function onFetchCPIFRMastersSuccess(response) {
  return { type: ON_FETCH_CPIFR_MASTERS_SUCCESS, response };
}

function onFetchCPIFRMastersFailure(response) {
  return { type: ON_FETCH_CPIFR_MASTERS_FAILURE, response };
}

function onFetchCPFIRMasters() {
  return function (dispatch) {
    dispatch(onFetchCPIFRMastersLoading());
    return fetchCPIFRMasters().then(
      (success) => dispatch(onFetchCPIFRMastersSuccess(success)),
      (error) => dispatch(onFetchCPIFRMastersFailure(error))
    );
  };
}

function fetchClosedCasesCPIFR(formData) {
  return client({
    method: 'POST',
    url: `casereview/cpifr/closed/cases/bucket`,
    data: formData,
    badRequestMessage: 'Unable to fetch closed cases. Please update filters.'
  });
}

function onFetchClosedCasesCPIFRLoading() {
  return { type: ON_FETCH_CASE_CLOSED_CPIFR_LOADING };
}

function onFetchClosedCasesCPIFRSuccess(response, conf) {
  return { type: ON_FETCH_CASE_CLOSED_CPIFR_SUCCESS, response, conf };
}

function onFetchClosedCasesCPIFRFailure(response) {
  return { type: ON_FETCH_CASE_CLOSED_CPIFR_FAILURE, response };
}

function onFetchClosedCasesCPIFR(formData) {
  return function (dispatch) {
    dispatch(onFetchClosedCasesCPIFRLoading());
    return fetchClosedCasesCPIFR(formData).then(
      (success) => dispatch(onFetchClosedCasesCPIFRSuccess(success, formData)),
      (error) => dispatch(onFetchClosedCasesCPIFRFailure(error))
    );
  };
}

function addCasesForReporting(formData) {
  return client({
    method: 'POST',
    url: `casereview/cpifr/create/incident`,
    data: formData,
    badRequestMessage: 'Unable to add cases for Reporting. Please verify all input details.'
  });
}

function onAddCasesForReporting(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return addCasesForReporting(formData)
      .then(
        (success) => dispatch(onShowSuccessAlert(success)),
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchCPIFRReportHistory(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/cpifr/download/history`,
    data: formData,
    badRequestMessage: 'Unable to get reporting history for current transaction.'
  });
}

function onFetchCPIFRReportHistoryLoading() {
  return { type: ON_FETCH_CPIFR_REPORT_HISTORY_LOADING };
}

function onFetchCPIFRReportHistorySuccess(response) {
  return { type: ON_FETCH_CPIFR_REPORT_HISTORY_SUCCESS, response };
}

function onFetchCPIFRReportHistoryFailure(response) {
  return { type: ON_FETCH_CPIFR_REPORT_HISTORY_FAILURE, response };
}

function onFetchCPIFRReportHistory(formData) {
  return function (dispatch) {
    dispatch(onFetchCPIFRReportHistoryLoading());
    return fetchCPIFRReportHistory(formData).then(
      (success) => dispatch(onFetchCPIFRReportHistorySuccess(success)),
      (error) => dispatch(onFetchCPIFRReportHistoryFailure(error))
    );
  };
}

function updateFRN(formData) {
  return client({
    method: 'POST',
    url: `casereview/cpifr/update/frn`,
    data: formData,
    badRequestMessage: 'Unable to update FRN. Please verify input details.'
  });
}

function onUpdateFRN(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return updateFRN(formData)
      .then(
        () => {
          dispatch(onShowSuccessAlert('FRN updated successfully'));
          dispatch(onFetchAllIncidents());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function onClearSelectedIncident() {
  return { type: ON_CLEAR_SELECTED_INCIDENT };
}

function saveCPFIRReport(formData) {
  return client({
    method: 'POST',
    url: `casereview/cpifr/incident/save/draft/report`,
    data: formData,
    badRequestMessage: 'Unable to save CPFIR Report. Please verify all input details.'
  });
}

function onSaveCPFIRReport(formData) {
  return function (dispatch, getState) {
    const selectedCase = getState().caseAssignment?.selectedCase;
    dispatch(onToggleLoader(true));
    return saveCPFIRReport(formData)
      .then(
        (success) => {
          dispatch(onShowSuccessAlert({ message: success }));
          if (selectedCase?.caseRefNo)
            dispatch(onUpdateCaseDetails(selectedCase?.caseRefNo, null, selectedCase?.channel));
          dispatch(onFetchAllIncidents());
          dispatch(onToggleCPIFRFormModal());
          dispatch(onClearSelectedIncident());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchUnverifiedCases(formData, channel) {
  return client({
    method: 'POST',
    url: `casereview/case/${formData.role}/${channel}/bucket`,
    data: formData,
    badRequestMessage: 'Unable to get unverified cases'
  });
}

function onFetchUnverifiedCasesLoading(channel, conf) {
  return { type: ON_FETCH_UNVERIFIED_CASES_LOADING, channel, conf };
}

function onFetchUnverifiedCasesSuccess(response, conf, channel) {
  return {
    type: ON_FETCH_UNVERIFIED_CASES_SUCCESS,
    response,
    conf,
    channel
  };
}

function onFetchUnverifiedCasesFailure(response, conf, channel) {
  return {
    type: ON_FETCH_UNVERIFIED_CASES_FAILURE,
    response,
    conf,
    channel
  };
}

function onFetchUnverifiedCases(conf, channel = 'frm') {
  return function (dispatch) {
    dispatch(onFetchUnverifiedCasesLoading(channel, conf));
    return fetchUnverifiedCases(conf, channel).then(
      (success) => dispatch(onFetchUnverifiedCasesSuccess(success, conf, channel)),
      (error) => dispatch(onFetchUnverifiedCasesFailure(error, conf, channel))
    );
  };
}

function onRemoveCasesFromUnverifiedList(cases) {
  return {
    type: ON_REMOVE_CASES_FROM_UNVERIFIED_LIST,
    cases
  };
}

export {
  onCreateIncidents,
  onFetchAllIncidents,
  onFetchIncidentById,
  onSubmitCPFIRReport,
  onFetchCPFIRMasters,
  onFetchClosedCasesCPIFR,
  onAddCasesForReporting,
  onFetchIncidentByIdSuccess,
  onFetchCPIFRReportHistory,
  onUpdateFRN,
  onClearSelectedIncident,
  onSaveCPFIRReport,
  onFetchUnverifiedCases,
  onRemoveCasesFromUnverifiedList
};
