'use strict';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner, faPencil, faTrash } from '@fortawesome/free-solid-svg-icons';
import { ButtonGroup, Button, FormGroup, Label, Input, TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import ConfirmAlert from 'components/common/ConfirmAlert';
import CardContainer from 'components/common/CardContainer';
import ExcelDownload from 'components/common/ExcelDownload';
import ModalContainer from 'components/common/ModalContainer';
import { MAX_AMOUNT } from 'constants/applicationConstants';

const MccLimit = ({
  toggle,
  toggleActions,
  actions,
  prefiltersList,
  currentPrefilterList,
  listType,
  role
}) => {
  const [mcc, setMcc] = useState('');
  const [amountLimit, setAmountLimit] = useState('');
  const [fileData, setFileData] = useState('');
  const [singleUploadLoading, setSingleUploadLoading] = useState(false);
  const [isEditmode, setIsEditmode] = useState(false);

  useEffect(() => {
    actions.onFetchLimitList(currentPrefilterList);
  }, []);

  const removeFilter = () => {
    let formData = {
      type: mcc,
      keyName: 'mcc'
    };

    actions.onDeleteLimitListItem(formData, currentPrefilterList, listType);
  };

  const toggleConfirmAlertModal = (mcc) => {
    mcc && setMcc(mcc);
    toggleActions.onToggleConfirmAlertModal(listType);
  };

  const setUpdatedValue = (filterData) => {
    setMcc(filterData.mcc);
    setAmountLimit(filterData.amountLimit);
    setIsEditmode(true);
  };

  const clearUpdatedValue = () => {
    setMcc('');
    setAmountLimit('');
    setIsEditmode(false);
  };

  const toggleFilterModal = (type, filterData) => {
    type == 'edit' ? setUpdatedValue(filterData) : clearUpdatedValue();

    toggleActions.onTogglePrefiltersListModal(listType);
  };

  const submitSingleEntry = (e) => {
    e.preventDefault();
    setSingleUploadLoading(true);
    let formData = {
      mcc,
      amountLimit: parseFloat(amountLimit)
    };

    if (isEditmode) {
      actions.onUpdateLimitList(formData, currentPrefilterList, listType);
    } else {
      actions.onAddSingleItemToLimitList(formData, currentPrefilterList, listType);
    }

    setSingleUploadLoading(false);
    setMcc('');
    setAmountLimit('');
  };

  const addInBulkEntity = (e) => {
    e.preventDefault();
    let formData = {
      [currentPrefilterList.prefilterValue]: fileData
    };
    actions.onAddItemsInBulkToLimitList(formData, currentPrefilterList, listType);
    setFileData('');
  };

  const header = [
    {
      Header: 'Actions',
      minWidth: 50,
      filterable: false,
      sortable: false,
      show: role == 'checker',
      Cell: (row) => (
        <span className="d-flex justify-content-start">
          <Button
            outline
            size="sm"
            color="warning"
            title="edit"
            onClick={() => toggleFilterModal('edit', row.original)}>
            <FontAwesomeIcon icon={faPencil} />
          </Button>
          <Button
            outline
            size="sm"
            color="danger"
            title="Delete"
            className="ms-1"
            onClick={() => toggleConfirmAlertModal(row.original.mcc)}>
            <FontAwesomeIcon icon={faTrash} />
          </Button>
        </span>
      )
    },
    { Header: 'MCC', accessor: 'mcc' },
    { Header: 'Amount Limit', accessor: 'amountLimit' }
  ];

  let excelHeaders = [
    { label: 'MCC', value: 'mcc' },
    { label: 'Amount Limit', value: 'amountLimit' }
  ];

  let action = (
    <ButtonGroup>
      <ExcelDownload
        data={prefiltersList.limitList.data}
        sheetName="MCCLimit"
        headers={excelHeaders}
      />
      {role == 'checker' && (
        <Button size="sm" color="primary" className="ms-1" onClick={() => toggleFilterModal('add')}>
          {`Add ${currentPrefilterList.prefilterName}`}
        </Button>
      )}
    </ButtonGroup>
  );

  return (
    <div>
      <CardContainer title={currentPrefilterList.prefilterName} action={action}>
        <ReactTable
          defaultFilterMethod={(filter, row) =>
            row[filter.id] &&
            row[filter.id].toString().toLowerCase().includes(filter.value.toLowerCase())
          }
          columns={header}
          data={prefiltersList.limitList.data}
          noDataText="No data found"
          filterable
          showPaginationTop={true}
          showPaginationBottom={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          defaultPageSize={10}
          minRows={3}
          className={'-highlight  -striped'}
        />
      </CardContainer>

      <ModalContainer
        size="md"
        theme={toggle.theme}
        isOpen={toggle.prefiltersListModal[listType]}
        toggle={() => toggleFilterModal('close')}
        header={`Add ${currentPrefilterList.prefilterName}`}>
        <Tabs tabNames={isEditmode ? ['Single'] : ['Single', 'Bulk']}>
          <TabPane tabId={0}>
            <form onSubmit={submitSingleEntry}>
              <FormGroup>
                <Label for="mcc">MCC</Label>
                <Input
                  type="text"
                  id="mcc"
                  name="mcc"
                  value={mcc}
                  onChange={(event) => {
                    setMcc(event.target.value);
                  }}
                  required
                  readOnly={isEditmode}
                />
              </FormGroup>
              <FormGroup>
                <Label for="amountLimit">Amount Limit</Label>
                <Input
                  type="number"
                  id="amountLimit"
                  name="amountLimit"
                  value={amountLimit}
                  onChange={(event) => {
                    setAmountLimit(event.target.value);
                  }}
                  required
                  min={0}
                  max={MAX_AMOUNT}
                  step={0.01}
                  pattern="^[0-9]*\.[0-9]{0,2}$"
                />
              </FormGroup>
              <FormGroup className="d-flex justify-content-end">
                <Button size="sm" type="submit" color="success" disabled={singleUploadLoading}>
                  {singleUploadLoading ? (
                    <FontAwesomeIcon icon={faSpinner} className={'loader fa-spin'} />
                  ) : (
                    'Save'
                  )}
                </Button>
              </FormGroup>
            </form>
          </TabPane>
          <TabPane tabId={1}>
            <form onSubmit={addInBulkEntity}>
              <FormGroup>
                <Input
                  name="fileUpload"
                  accept=".csv"
                  type="file"
                  files={fileData}
                  onChange={(event) => setFileData(event.target.files[0])}
                  required
                />
              </FormGroup>
              <FormGroup className="d-flex justify-content-end">
                <Button size="sm" type="submit" color="success" disabled={toggle.uploadLoader}>
                  Upload
                </Button>
              </FormGroup>
            </form>
          </TabPane>
        </Tabs>
      </ModalContainer>

      <ConfirmAlert
        theme={toggle.theme}
        confirmAlertModal={toggle.confirmAlertModal[listType]}
        toggleConfirmAlertModal={toggleConfirmAlertModal}
        confirmationAction={removeFilter}
        confirmAlertTitle={'Are you sure you want to delete it ?'}
      />
    </div>
  );
};

MccLimit.propTypes = {
  toggle: PropTypes.object.isRequired,
  toggleActions: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  prefiltersList: PropTypes.object.isRequired,
  currentPrefilterList: PropTypes.object.isRequired,
  listType: PropTypes.string.isRequired,
  role: PropTypes.string.isRequired
};

export default MccLimit;
