import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { <PERSON>dal, ModalHeader, ModalBody, Modal<PERSON>ooter, Button, FormGroup, Label, Input } from 'reactstrap';
import _ from 'lodash';
import MoneyMuleReportFormContainer from 'containers/investigation/MoneyMuleReportFormContainer';
import { isCooperative } from 'constants/publicKey';

const VerdictModalMoneyMule = ({
  theme,
  userList,
  caseRefNo,
  bulkCaseIds,
  closeCase,
  fileMoneyMule,
  gosDetails,
  getGosDetails,
  singleType = '',
  showText = false,
  disabled = false,
  childTxns = []
}) => {
  const [displayModal, setDisplayModal] = useState(false);
  const [report, setReport] = useState('');
  const [notation, setNotation] = useState('');
  const [selectedChecker, setSelectedChecker] = useState('');

  const checkerList = _.filter(userList, (user) => _.includes(user.channelRoles, 'moneyMule:checker'));
  const poList = userList?.filter((user) => user.channelRoles?.includes('moneyMule:principal-officer'));

  useEffect(() => {
    !gosDetails.loader &&
      caseRefNo &&
      singleType !== '/singleFromTable' &&
      getGosDetails(caseRefNo);
  }, []);

  const closeModal = () => {
    setDisplayModal(false);
    setReport('');
    setNotation('');
    setSelectedChecker('');
  };

  const submitVerdict = () => {
    if (!report || !notation) return;

    let formData = {
      channel: 'moneyMule',
      remark: notation,
      ...(!_.isEmpty(bulkCaseIds) ? { caseRefNos: bulkCaseIds } : { caseRefNo }),
      childTxns,
      ...(report === 'moneyMule' && {
        ...gosDetails.data,
        checkerToBeEscalatedTo:
          isCooperative || checkerList.length === 0
            ? `${poList[0].id}`
            : checkerList.length === 1
            ? `${checkerList[0].id}`
            : selectedChecker
      })
    };

    if (report === 'moneyMule') {
      fileMoneyMule(formData);
    } else {
      closeCase(formData);
    }
    closeModal();
  };

  return (
    <>
      <Button
        outline
        size="sm"
        color="primary"
        className="ms-1"
        title="Suggest Action"
        disabled={disabled}
        onClick={() => setDisplayModal(true)}>
        {showText ? 'Suggest Action' : 'Action'}
      </Button>

      <Modal isOpen={displayModal} toggle={closeModal} size="lg" className={`theme-${theme}`}>
        <ModalHeader toggle={closeModal}>Suggest Action</ModalHeader>
        <ModalBody>
          <FormGroup>
            <Label>Report</Label>
            <Input
              type="select"
              name="report"
              id="report"
              value={report}
              onChange={(e) => setReport(e.target.value)}
              required>
              <option value="">-- SELECT --</option>
              <option value="moneyMule">File MoneyMule Report</option>
              <option value="close">Close with false positive</option>
            </Input>
          </FormGroup>
          <FormGroup>
            <Label>Notation</Label>
            <Input
              type="textarea"
              name="notation"
              id="notation"
              value={notation}
              onChange={(e) => setNotation(e.target.value)}
              required
            />
          </FormGroup>
          {report === 'moneyMule' && <MoneyMuleReportFormContainer caseId={caseRefNo} />}
          {report === 'moneyMule' && !isCooperative && checkerList.length > 1 && (
            <FormGroup>
              <Label>Checker</Label>
              <Input
                type="select"
                name="checker"
                id="checker"
                value={selectedChecker}
                onChange={(e) => setSelectedChecker(e.target.value)}
                required>
                {_.map(checkerList, (checker) => (
                  <option key={checker.id} value={checker.id}>
                    {checker.userName}
                  </option>
                ))}
              </Input>
            </FormGroup>
          )}
        </ModalBody>
        <ModalFooter>
          <Button color="primary" onClick={submitVerdict} disabled={!report || !notation}>
            Submit
          </Button>
          <Button color="secondary" onClick={closeModal}>
            Cancel
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

VerdictModalMoneyMule.propTypes = {
  theme: PropTypes.string.isRequired,
  userList: PropTypes.array.isRequired,
  caseRefNo: PropTypes.string,
  bulkCaseIds: PropTypes.array,
  closeCase: PropTypes.func.isRequired,
  fileMoneyMule: PropTypes.func.isRequired,
  gosDetails: PropTypes.object.isRequired,
  getGosDetails: PropTypes.func.isRequired,
  singleType: PropTypes.string,
  showText: PropTypes.bool,
  disabled: PropTypes.bool,
  childTxns: PropTypes.array
};

export default VerdictModalMoneyMule;
