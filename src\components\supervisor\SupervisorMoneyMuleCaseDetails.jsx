import React, { Suspense, lazy, useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { TabPane, Row, Col } from 'reactstrap';
import { useHistory, useParams } from 'react-router-dom';
import _ from 'lodash';

import Tabs from 'components/common/Tabs';
import Loader from 'components/loader/Loader';
import LogContainer from 'containers/common/LogContainer';
import NotationContainer from 'containers/common/NotationContainer';
import CitationContainer from 'containers/common/CitationContainer';
import CaseDetailCard from 'containers/common/CaseDetailCardContainer';
import MoneyMuleReportContainer from 'containers/investigation/MoneyMuleReportContainer';
import AgentInfoCardContainer from 'containers/common/AgentInfoCardContainer';
import ApprovalModalContainer from 'containers/common/ApprovalModalContainer';
import DownloadMoneyMuleButton from 'containers/investigation/DownloadMoneyMuleButtonContainer';

const CustomerInfoCardContainer = lazy(() => import('containers/common/CustomerInfoCardContainer'));
const MerchantInfoCardContainer = lazy(() => import('containers/common/MerchantInfoCardContainer'));
const TransactionDetailCardContainer = lazy(() => import('containers/common/TransactionDetailCardContainer'));

const SupervisorMoneyMuleCaseDetails = ({
  role,
  userName,
  txnDetails,
  selectCase,
  selectedCase,
  transactionHistorySearchActions
}) => {
  const history = useHistory();
  const { txnId } = useParams();
  const tabRef = useRef(null);
  const [renderedTab, setRenderedTab] = useState([]);

  useEffect(() => {
    txnId && selectCase({ txnId, channel: 'moneyMule' });
    tabRef?.current?.changeTab(0);
  }, [txnId]);

  useEffect(() => {
    if (!_.isEmpty(selectedCase)) {
      document.title = 'BANKiQ FRC | Supervisor - ' + selectedCase.caseRefNo;
      transactionHistorySearchActions.onClearSearch();
    }

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, [selectedCase]);

  const tabChangeAction = (tabId) => {
    if (!_.includes(renderedTab, tabId)) setRenderedTab((prev) => [...prev, tabId]);
  };

  const entityId = _.has(txnDetails.details, 'entityId') ? txnDetails.details.entityId.value : '';

  const tabList = ['Details', 'MoneyMule Report', 'Logs', 'Notations', 'Citations'];

  const supervisorAction = (
    <div className="d-flex">
      <DownloadMoneyMuleButton caseRefNo={selectedCase?.caseRefNo} entityId={entityId} />
      <ApprovalModalContainer />
    </div>
  );

  return (
    <div className={'content-wrapper'}>
      <Tabs
        pills
        ref={tabRef}
        tabNames={tabList}
        action={supervisorAction}
        tabChangeAction={tabChangeAction}>
        <TabPane tabId={0}>
          <Row className="ms-0">
            <Col md="12" sm="12">
              {selectedCase.currentStatus !== 'READY_TO_OPEN' && (
                <CaseDetailCard caseDetails={selectedCase} channel="moneyMule" />
              )}
              <TransactionDetailCardContainer channel="moneyMule" />
              <Suspense fallback={<Loader show={true} />}>
                {_.lowerCase(txnDetails.details.entityCategory) === 'merchant' ? (
                  <MerchantInfoCardContainer merchantId={entityId} channel="moneyMule" />
                ) : _.lowerCase(txnDetails.details.entityCategory) === 'customer' ? (
                  <CustomerInfoCardContainer customerId={entityId} channel="moneyMule" />
                ) : _.lowerCase(txnDetails.details.entityCategory) === 'agent' ? (
                  <AgentInfoCardContainer agentId={entityId} channel="moneyMule" />
                ) : null}
              </Suspense>
            </Col>
          </Row>
        </TabPane>

        {selectedCase.currentStage !== 'Maker' &&
          selectedCase.currentStatus !== 'READY_TO_OPEN' && (
            <TabPane tabId={1}>
              {_.includes(renderedTab, 1) && (
                <Suspense fallback={<Loader show={true} />}>
                  <MoneyMuleReportContainer caseRefNo={selectedCase?.caseRefNo} />
                </Suspense>
              )}
            </TabPane>
          )}

        <TabPane tabId={2}>
          {_.includes(renderedTab, 2) && (
            <LogContainer
              caseRefNo={selectedCase?.caseRefNo}
              txnId={txnId}
              channel="moneyMule"
            />
          )}
        </TabPane>

        <TabPane tabId={3}>
          {_.includes(renderedTab, 3) && (
            <NotationContainer
              caseId={selectedCase.caseId}
              caseRefNo={selectedCase.caseRefNo}
              channel="moneyMule"
              disableComment={true}
            />
          )}
        </TabPane>

        <TabPane tabId={4}>
          {selectedCase.caseRefNo && _.includes(renderedTab, 4) && (
            <Suspense fallback={<Loader show={true} />}>
              <CitationContainer
                caseRefNo={selectedCase.caseRefNo}
                txnId={txnId}
                disable={true}
              />
            </Suspense>
          )}
        </TabPane>
      </Tabs>
    </div>
  );
};

SupervisorMoneyMuleCaseDetails.propTypes = {
  role: PropTypes.string.isRequired,
  userName: PropTypes.string.isRequired,
  txnDetails: PropTypes.object.isRequired,
  selectedCase: PropTypes.object.isRequired,
  transactionHistorySearchActions: PropTypes.object.isRequired,
  selectCase: PropTypes.func.isRequired
};

export default SupervisorMoneyMuleCaseDetails;
