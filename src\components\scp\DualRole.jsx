import React, { useState } from 'react';
import PropTypes from 'prop-types';
import ConfigFormWrapper from 'components/scp/ConfigFormWrapper';
import {
  inputChangeDataHandler,
  saveConfigurationsDataHandler,
  resetConfigurationsDataHandler
} from 'components/scp/scpFunctions';

function DualRole({ highlightText, saveConfigurations, configurationsData }) {
  const [dualRole, setDualRole] = useState({
    isEdited: false,
    ...configurationsData.configPoints
  });

  return (
    <ConfigFormWrapper
      configTitle="Dual Role"
      activationId="activationDualRole"
      data={dualRole}
      highlightText={highlightText}
      handleSaveConfigurations={(event) =>
        saveConfigurationsDataHandler(
          event,
          configurationsData,
          dualRole,
          setDualRole,
          saveConfigurations
        )
      }
      handleInputChange={(event) => inputChangeDataHandler(event, dualRole, setDualRole)}
      handleResetConfigurations={() =>
        resetConfigurationsDataHandler(configurationsData, setDualRole)
      }
    />
  );
}

DualRole.propTypes = {
  highlightText: PropTypes.func.isRequired,
  saveConfigurations: PropTypes.func.isRequired,
  configurationsData: PropTypes.object.isRequired
};

export default DualRole;
