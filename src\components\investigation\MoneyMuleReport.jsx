import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Row, Col } from 'reactstrap';
import { isEmpty, has } from 'lodash';

const MoneyMuleReport = ({ caseRefNo, moneyMuleReportDetails, moneyMuleReportMasters, getMasters, getMoneyMuleReport }) => {
  const { data, loader, error } = moneyMuleReportDetails;
  const getDataFromReport = (key) =>
    !loader && !error && !isEmpty(data) && has(data, key) && data[key];

  useEffect(() => {
    isEmpty(moneyMuleReportMasters?.data) && getMasters();
  }, []);

  useEffect(() => {
    caseRefNo && getMoneyMuleReport(caseRefNo);
  }, [caseRefNo]);

  const formStructuredInput = (id, label, masterField) => (
    <Row className="ms-5 me-5">
      <Col md="6">
        <label htmlFor={id} className="form-label">
          {label}
        </label>
      </Col>
      <Col md="6">
        <span className="form-control-plaintext">
          {getDataFromReport(masterField) || 'Not Available'}
        </span>
      </Col>
    </Row>
  );

  if (loader) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '200px' }}>
        <div className="spinner-border" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        Error loading MoneyMule report details
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <h5 className="card-title mb-0">MoneyMule Report Details</h5>
      </div>
      <div className="card-body">
        {formStructuredInput('roleMainAssociate', 'Role of Main Associate', 'roleMainAssociate')}
        {formStructuredInput('sourceOfFunds', 'Source of Funds', 'sourceOfFunds')}
        {formStructuredInput('destinationOfFunds', 'Destination of Funds', 'destinationOfFunds')}
        {formStructuredInput('sourceOfAlert', 'Source of Alert', 'sourceOfAlert')}
        {formStructuredInput('suspicionDueTo', 'Suspicion Due To', 'suspicionDueTo')}
        {formStructuredInput('redFlagIndicator', 'Red Flag Indicator', 'redFlagIndicator')}
        {formStructuredInput('typeOfSuspicion', 'Type of Suspicion', 'typeOfSuspicion')}
        {formStructuredInput('narration', 'Narration', 'narration')}
      </div>
    </div>
  );
};

MoneyMuleReport.propTypes = {
  caseRefNo: PropTypes.string.isRequired,
  moneyMuleReportDetails: PropTypes.object.isRequired,
  moneyMuleReportMasters: PropTypes.object.isRequired,
  getMasters: PropTypes.func.isRequired,
  getMoneyMuleReport: PropTypes.func.isRequired
};

export default MoneyMuleReport;
