{"name": "ifrm-ui", "version": "v7.0.0", "description": "React Redux based UI application for BankIQ IFRM", "engines": {"npm": ">=10", "node": ">=18.20"}, "scripts": {"start-message": "babel-node tools/startMessage.js", "prestart": "npm run start-message", "start": "concurrently -k -r -s first \"npm run test:watch\" \"npm run open:src\" \"npm run lint:watch\"", "security-check": "nsp check", "open:src": "babel-node tools/srcServer.js", "open:dist": "babel-node tools/distServer.js", "lint": "eslint webpack.* src tools --color", "lint:watch": "chokidar \"src/**/*.js\" \"src/**/*.jsx\" \"webpack.*\" -c \"npm run lint\"", "lint:fix": "eslint webpack.* src tools --color --fix", "clean-dist": "npm run remove-dist && mkdir dist", "remove-dist": "rimraf ./dist", "prebuild": "npm run lint && npm run clean-dist", "build": "babel-node tools/build.js && npm run open:dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze-bundle": "babel-node tools/analyzeBundle.js", "prettier:write": "npx prettier --write \"**/*.{js,jsx,json}\"", "prettier:check": "npx prettier --check \"**/*.{js,jsx,json}\"", "lint-prettier:fix": "npm run lint:fix && npm run prettier:write"}, "author": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "dependencies": {"@fortawesome/fontawesome-svg-core": "6.7.2", "@fortawesome/free-regular-svg-icons": "6.7.2", "@fortawesome/free-solid-svg-icons": "6.7.2", "@fortawesome/react-fontawesome": "0.2.2", "bootstrap": "5.3.3", "config": "1.31.0", "crypto-browserify": "3.12.1", "echarts": "5.6.0", "echarts-for-react": "3.0.2", "html2canvas": "^1.4.1", "jquery": "3.7.1", "js-file-download": "^0.4.12", "jsencrypt": "3.3.2", "lodash": "4.17.21", "moment": "2.30.1", "object-assign": "4.1.1", "react": "17.0.2", "react-csv": "^2.2.2", "react-datetime": "3.3.1", "react-dom": "17.0.2", "react-idle-timer": "4.6.4", "react-multi-select-component": "4.3.4", "react-redux": "^7.2.6", "react-router-dom": "5.3.4", "react-table": "6.11.5", "react-to-pdf": "2.0.0", "reactstrap": "9.2.3", "redux": "^4.1.2", "redux-persist": "6.0.0", "redux-persist-crosstab": "3.7.0", "redux-thunk": "^2.1.0", "string_decoder": "1.3.0", "use-react-screenshot": "^3.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.22.15", "@babel/eslint-parser": "^7.27.0", "@babel/node": "^7.22.15", "@babel/plugin-proposal-decorators": "7.25.9", "@babel/plugin-proposal-do-expressions": "7.25.9", "@babel/plugin-proposal-export-default-from": "7.25.9", "@babel/plugin-proposal-function-sent": "7.25.9", "@babel/plugin-proposal-pipeline-operator": "7.26.7", "@babel/plugin-proposal-throw-expressions": "7.25.9", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-transform-numeric-separator": "7.25.9", "@babel/plugin-transform-react-constant-elements": "7.25.9", "@babel/preset-env": "^7.22.15", "@babel/preset-react": "^7.22.15", "@eslint/compat": "^1.2.7", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.23.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^12.1.2", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "autoprefixer": "10.4.21", "babel-jest": "29.7.0", "babel-loader": "^8.0.0", "browser-sync": "3.0.3", "caniuse-lite": "^1.0.30001707", "chalk": "4.1.2", "chokidar-cli": "^3.0.0", "concurrently": "3.6.1", "connect-history-api-fallback": "1.6.0", "copy-webpack-plugin": "^13.0.0", "coveralls": "3.1.1", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.2", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.5", "eslint-plugin-react": "^7.37.4", "globals": "^16.0.0", "history": "4.10.1", "html-webpack-plugin": "^5.6.3", "jest": "^28.1.1", "jest-environment-jsdom": "^29.7.0", "jest-fixed-jsdom": "^0.0.9", "mini-css-extract-plugin": "^2.9.2", "mockdate": "2.0.5", "msw": "^2.6.4", "node-fetch": "2.7.0", "postcss-loader": "7.3.4", "prettier": "^3.5.3", "prop-types": "15.8.1", "raf": "3.4.1", "react-refresh": "^0.16.0", "react-test-renderer": "17.0.2", "redux-immutable-state-invariant": "2.1.0", "redux-mock-store": "^1.3.0", "sass": "1.86.0", "sass-loader": "^16.0.5", "schema-utils": "^4.3.0", "source-map-loader": "1.1.3", "style-loader": "^4.0.0", "webpack": "^5.98.0", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "^6.0.1", "webpack-dev-middleware": "5.3.4", "webpack-dev-server": "^5.2.0", "webpack-hot-middleware": "2.26.1"}, "keywords": [], "repository": {"type": "git", "url": "https://github.com/SQ-ONE/ifrm-ui"}}