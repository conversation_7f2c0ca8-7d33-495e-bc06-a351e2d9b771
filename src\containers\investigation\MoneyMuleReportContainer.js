import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  onFetchMoneyMuleReportMasters,
  onFetchMoneyMuleReportDetails
} from 'actions/moneyMuleReportActions';
import MoneyMuleReport from 'components/investigation/MoneyMuleReport';

const mapStateToProps = (state) => {
  return {
    moneyMuleReportDetails: state.moneyMuleReport.details,
    moneyMuleReportMasters: state.moneyMuleReport.masters
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    getMasters: bindActionCreators(onFetchMoneyMuleReportMasters, dispatch),
    getMoneyMuleReport: bindActionCreators(onFetchMoneyMuleReportDetails, dispatch)
  };
};

const MoneyMuleReportContainer = connect(mapStateToProps, mapDispatchToProps)(MoneyMuleReport);

export default MoneyMuleReportContainer;
