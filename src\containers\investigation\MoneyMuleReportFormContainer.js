import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchMoneyMuleReportMasters } from 'actions/moneyMuleReportActions';
import MoneyMuleReportForm from 'components/investigation/MoneyMuleReportForm';

const mapStateToProps = (state) => {
  return {
    moneyMuleReportMasters: state.moneyMuleReport.masters,
    violations: state.violatedRules,
    citations: state.citations,
    caseDocument: state.caseDocument
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    getMasters: bindActionCreators(onFetchMoneyMuleReportMasters, dispatch)
  };
};

const MoneyMuleReportFormContainer = connect(mapStateToProps, mapDispatchToProps)(MoneyMuleReportForm);

export default MoneyMuleReportFormContainer;
