import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchMoneyMuleReportDetails } from 'actions/moneyMuleReportActions';
import { onCloseCase, onRequestSupervisorApproval } from 'actions/caseReviewActions';
import VerdictModalMoneyMule from 'components/common/VerdictModalMoneyMule';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    userList: state.user.userslist,
    verdictModal: state.toggle.verdictModal.moneyMule,
    citations: state.citations,
    gosDetails: state.moneyMuleReport.details
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    closeCase: bindActionCreators(onCloseCase, dispatch),
    fileMoneyMule: bindActionCreators(onRequestSupervisorApproval, dispatch),
    getGosDetails: bindActionCreators(onFetchMoneyMuleReportDetails, dispatch)
  };
};

const VerdictModalMoneyMuleContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(VerdictModalMoneyMule);

export default VerdictModalMoneyMuleContainer;
