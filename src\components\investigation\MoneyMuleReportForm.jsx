import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, FormGroup, Label, Input } from 'reactstrap';
import { isEmpty } from 'lodash';

const MoneyMuleReportForm = ({ caseId, moneyMuleReportMasters, getMasters }) => {
  const [formData, setFormData] = useState({
    roleMainAssociate: '',
    sourceOfFunds: '',
    destinationOfFunds: '',
    sourceOfAlert: '',
    suspicionDueTo: '',
    redFlagIndicator: '',
    otherRedFlagIndicator: '',
    typeOfSuspicion: '',
    narration: ''
  });

  useEffect(() => {
    isEmpty(moneyMuleReportMasters?.data) && getMasters();
  }, []);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const renderSelectField = (id, label, options, value, onChange) => (
    <FormGroup>
      <Label htmlFor={id}>{label}</Label>
      <Input
        type="select"
        id={id}
        name={id}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        required>
        <option value="">-- SELECT --</option>
        {options?.map((option, index) => (
          <option key={index} value={option.value || option}>
            {option.label || option}
          </option>
        ))}
      </Input>
    </FormGroup>
  );

  const renderTextAreaField = (id, label, value, onChange) => (
    <FormGroup>
      <Label htmlFor={id}>{label}</Label>
      <Input
        type="textarea"
        id={id}
        name={id}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        rows="4"
        required
      />
    </FormGroup>
  );

  const masters = moneyMuleReportMasters?.data || {};

  return (
    <div className="card">
      <div className="card-header">
        <h5 className="card-title mb-0">MoneyMule Report Form</h5>
      </div>
      <div className="card-body">
        <Row>
          <Col md="6">
            {renderSelectField(
              'roleMainAssociate',
              'Role of Main Associate',
              masters.roleMainAssociate || [],
              formData.roleMainAssociate,
              (value) => handleInputChange('roleMainAssociate', value)
            )}
          </Col>
          <Col md="6">
            {renderSelectField(
              'sourceOfFunds',
              'Source of Funds',
              masters.sourceOfFunds || [],
              formData.sourceOfFunds,
              (value) => handleInputChange('sourceOfFunds', value)
            )}
          </Col>
        </Row>
        <Row>
          <Col md="6">
            {renderSelectField(
              'destinationOfFunds',
              'Destination of Funds',
              masters.destinationOfFunds || [],
              formData.destinationOfFunds,
              (value) => handleInputChange('destinationOfFunds', value)
            )}
          </Col>
          <Col md="6">
            {renderSelectField(
              'sourceOfAlert',
              'Source of Alert',
              masters.sourceOfAlert || [],
              formData.sourceOfAlert,
              (value) => handleInputChange('sourceOfAlert', value)
            )}
          </Col>
        </Row>
        <Row>
          <Col md="6">
            {renderSelectField(
              'suspicionDueTo',
              'Suspicion Due To',
              masters.suspicionDueTo || [],
              formData.suspicionDueTo,
              (value) => handleInputChange('suspicionDueTo', value)
            )}
          </Col>
          <Col md="6">
            {renderSelectField(
              'redFlagIndicator',
              'Red Flag Indicator',
              masters.redFlagIndicator || [],
              formData.redFlagIndicator,
              (value) => handleInputChange('redFlagIndicator', value)
            )}
          </Col>
        </Row>
        {formData.redFlagIndicator?.toLowerCase() === 'others' && (
          <Row>
            <Col md="12">
              <FormGroup>
                <Label htmlFor="otherRedFlagIndicator">Other Red Flag Indicator</Label>
                <Input
                  type="text"
                  id="otherRedFlagIndicator"
                  name="otherRedFlagIndicator"
                  value={formData.otherRedFlagIndicator}
                  onChange={(e) => handleInputChange('otherRedFlagIndicator', e.target.value)}
                  required
                />
              </FormGroup>
            </Col>
          </Row>
        )}
        <Row>
          <Col md="12">
            {renderSelectField(
              'typeOfSuspicion',
              'Type of Suspicion',
              masters.typeOfSuspicion || [],
              formData.typeOfSuspicion,
              (value) => handleInputChange('typeOfSuspicion', value)
            )}
          </Col>
        </Row>
        <Row>
          <Col md="12">
            {renderTextAreaField(
              'narration',
              'Narration',
              formData.narration,
              (value) => handleInputChange('narration', value)
            )}
          </Col>
        </Row>
      </div>
    </div>
  );
};

MoneyMuleReportForm.propTypes = {
  caseId: PropTypes.string.isRequired,
  moneyMuleReportMasters: PropTypes.object.isRequired,
  getMasters: PropTypes.func.isRequired
};

export default MoneyMuleReportForm;
