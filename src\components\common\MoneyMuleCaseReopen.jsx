import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dalBody, <PERSON>dal<PERSON>ooter, Button, FormGroup, Label, Input } from 'reactstrap';
import _ from 'lodash';

const MoneyMuleCaseReopen = ({
  theme,
  role,
  usersList,
  userId,
  reOpenCase,
  caseRefNo,
  selectedCase,
  advanceSearchData,
  showText = false
}) => {
  const [display, setDisplay] = useState(false);
  const [reason, setReason] = useState('');
  const [selectedMaker, setSelectedMaker] = useState('');

  const moneyMuleMakersList = _.filter(usersList, (user) => _.includes(user.channelRoles, 'moneyMule:maker'));

  useEffect(() => {
    clearModalValue();
  }, [display]);

  const clearModalValue = () => {
    setReason('');
    setSelectedMaker('');
  };

  const submitReopen = () => {
    if (!reason || (role === 'principal-officer' && !selectedMaker)) return;

    const formData = {
      caseRefNo,
      reason,
      channel: 'moneyMule',
      ...(role === 'principal-officer' && { assignedTo: selectedMaker })
    };

    reOpenCase(formData, selectedCase, advanceSearchData);
    setDisplay(false);
  };

  return (
    <>
      <Button
        outline
        size="sm"
        color="warning"
        className="ms-1"
        title="Reopen Case"
        onClick={() => setDisplay(true)}>
        {showText ? 'Reopen Case' : 'Reopen'}
      </Button>

      <Modal isOpen={display} toggle={() => setDisplay(!display)} className={`theme-${theme}`}>
        <ModalHeader toggle={() => setDisplay(!display)}>Reopen MoneyMule Case</ModalHeader>
        <ModalBody>
          <FormGroup>
            <Label>Reason for Reopening</Label>
            <Input
              type="textarea"
              name="reason"
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Enter reason for reopening the case"
              required
            />
          </FormGroup>
          {role === 'principal-officer' && moneyMuleMakersList.length > 0 && (
            <FormGroup>
              <Label>Assign to Maker</Label>
              <Input
                type="select"
                name="maker"
                id="maker"
                value={selectedMaker}
                onChange={(e) => setSelectedMaker(e.target.value)}
                required>
                <option value="">-- SELECT MAKER --</option>
                {_.map(moneyMuleMakersList, (maker) => (
                  <option key={maker.id} value={maker.id}>
                    {maker.userName}
                  </option>
                ))}
              </Input>
            </FormGroup>
          )}
        </ModalBody>
        <ModalFooter>
          <Button
            color="primary"
            onClick={submitReopen}
            disabled={!reason || (role === 'principal-officer' && !selectedMaker)}>
            Reopen Case
          </Button>
          <Button color="secondary" onClick={() => setDisplay(false)}>
            Cancel
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

MoneyMuleCaseReopen.propTypes = {
  theme: PropTypes.string.isRequired,
  role: PropTypes.string.isRequired,
  usersList: PropTypes.array.isRequired,
  userId: PropTypes.number.isRequired,
  reOpenCase: PropTypes.func.isRequired,
  caseRefNo: PropTypes.string.isRequired,
  selectedCase: PropTypes.object.isRequired,
  advanceSearchData: PropTypes.object.isRequired,
  showText: PropTypes.bool
};

export default MoneyMuleCaseReopen;
