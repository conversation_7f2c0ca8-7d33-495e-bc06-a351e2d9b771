'use strict';
import React, { useEffect, useRef, useState } from 'react';
import { isEmpty, assign, map } from 'lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/free-solid-svg-icons';
import PropTypes from 'prop-types';
import { FormGroup, Button, TabPane } from 'reactstrap';

import FormStepper from 'components/common/FormStepper';
import Sandbox from 'containers/ruleEngine/SandboxContainer';
import Checklist from 'containers/ruleEngine/ChecklistContainer';
import RuleBuilderContainer from 'containers/ruleEngine/RuleBuilderContainer';
import RuleDetailsFormContainer from 'containers/ruleEngine/RuleDetailsFormContainer';

const RuleForm = ({
  channel,
  formName,
  formData,
  ruleList,
  ruleCreation,
  fetchPrefilterLists,
  ruleCreationActions,
  submit,
  hasSandbox,
  moduleType,
  isSandbox = false
}) => {
  const ruleForm = useRef(null);
  const [ruleData, setRuleData] = useState({
    code: null,
    ruleType: channel === 'str' ? 'Str' : 'Dsl',
    logic: '',
    name: '',
    description: '',
    order: 0,
    assignmentPriority: 0,
    explicit: channel === 'str' || channel === 'moneyMule',
    actionCode: '01',
    actionName: 'ACCEPTED',
    methodType: channel == 'str' ? 'Post' : 'Pre',
    comments: '',
    isMerchantSpecific: false,
    lowLevelOutcome: null,
    medLevelOutcome: null,
    highLevelOutcome: null,
    alertCategoryId: -1,
    fraudCategory: '',
    label: '',
    citationNames: [],
    channels: '',
    isDelete: 0,
    ...formData
  });
  const [submitLoading, setSubmitLoading] = useState(false);
  const [invalidLogic, setInvalidLogic] = useState(false);
  const [active, setActive] = useState(0);
  const [disableDetailsNext, setDisableDetailsNext] = useState(true);

  let steppers = ['Details', 'Logic', 'Checklist'];
  channel !== 'str' && hasSandbox === 1 && steppers.push('Sandbox');

  let currentChannel =
    moduleType == 'issuer' && ruleData.methodType == 'Post' && channel == 'frm' ? 'nrt' : channel;

  const combinedRuleList = [...ruleList[channel], ...ruleCreation.nonProductionRules.list[channel]];

  useEffect(() => {
    if (isEmpty(ruleCreation.actionList)) ruleCreationActions.onFetchActionList(channel);
    if (isEmpty(ruleCreation.alertCategories)) ruleCreationActions.onFetchAlertCategories(channel);
    if (isEmpty(ruleCreation.ruleChannels)) ruleCreationActions.onFetchRuleChannelsList(channel);
    if (isEmpty(ruleCreation.fraudCategories))
      ruleCreationActions.onFetchRuleFraudCategoriesList(channel);
    if (isEmpty(ruleCreation.ruleLabels)) ruleCreationActions.onFetchRuleLabels(channel);
    fetchPrefilterLists();
  }, []);

  const updateRuleData = (id, value) =>
    setRuleData((prev) =>
      assign({}, prev, {
        [id]:
          id == 'alertCategoryId' || id == 'order' || id == 'assignmentPriority'
            ? parseInt(value)
            : value
      })
    );

  const onChecklistChange = (newChecklist) => {
    const citations = map(newChecklist, (item) => item.citationName);
    updateRuleData('citationNames', citations);
  };

  const onSubmit = (e) => {
    e.preventDefault();
    setSubmitLoading(true);
    if (disableDetailsNext || invalidLogic) return false;
    submit({ rule: ruleData, channel });
    setSubmitLoading(false);
  };

  return ruleCreation.loader ? (
    <FontAwesomeIcon icon={faSpinner} className={'loader fa-spin'} />
  ) : ruleCreation.error ? (
    <div className="no-data-div">{ruleCreation.errorMessage}</div>
  ) : (
    <>
      {!isSandbox ? (
        <form name={formName} onSubmit={onSubmit} ref={ruleForm}>
          <FormStepper steps={steppers} active={active}>
            <TabPane tabId={0}>
              <RuleDetailsFormContainer
                channel={channel}
                formName={formName}
                ruleData={ruleData}
                combinedRuleList={combinedRuleList}
                updateRuleData={updateRuleData}
                updateNextDisable={setDisableDetailsNext}
              />
              <FormGroup className="d-flex justify-content-end">
                <Button
                  color="primary"
                  type="button"
                  size="sm"
                  onClick={() => setActive(1)}
                  disabled={disableDetailsNext}>
                  Next
                </Button>
              </FormGroup>
            </TabPane>
            <TabPane tabId={1}>
              <RuleBuilderContainer
                channel={currentChannel}
                formName={formName}
                ruleData={ruleData}
                combinedRuleList={combinedRuleList}
                updateRuleData={updateRuleData}
                invalidateLogic={setInvalidLogic}
              />
              <FormGroup className="d-flex justify-content-between">
                <Button color="warning" type="button" size="sm" onClick={() => setActive(0)}>
                  Back
                </Button>
                <Button
                  color="primary"
                  type="button"
                  size="sm"
                  className="ms-2"
                  onClick={() => setActive(2)}
                  disabled={!ruleCreation.validation.status || invalidLogic || disableDetailsNext}>
                  Next
                </Button>
              </FormGroup>
            </TabPane>
            <TabPane tabId={2}>
              <Checklist
                onChecklistChange={onChecklistChange}
                code={formData?.code || ''}
                channel={channel}
              />
              <FormGroup className="d-flex justify-content-between mt-4">
                <Button color="warning" type="button" size="sm" onClick={() => setActive(1)}>
                  Back
                </Button>
                <span>
                  <Button
                    color="primary"
                    type="submit"
                    size="sm"
                    disabled={
                      !ruleCreation.validation.status || invalidLogic || disableDetailsNext
                    }>
                    {submitLoading ? (
                      <FontAwesomeIcon icon={faSpinner} className={'loader fa-spin'} />
                    ) : (
                      'Create'
                    )}
                  </Button>
                  {hasSandbox === 1 && channel !== 'str' && (
                    <Button
                      color="info"
                      type="button"
                      size="sm"
                      className="ms-2"
                      onClick={() => setActive(3)}
                      disabled={
                        !ruleCreation.validation.status || invalidLogic || disableDetailsNext
                      }>
                      Test and Create
                    </Button>
                  )}
                </span>
              </FormGroup>
            </TabPane>
            {channel !== 'str' && hasSandbox === 1 && (
              <TabPane tabId={3}>
                <Sandbox ruleList={ruleList[channel]} selectedRule={ruleData} />
                <FormGroup className="d-flex justify-content-between mt-4">
                  <Button color="warning" type="button" size="sm" onClick={() => setActive(1)}>
                    Back
                  </Button>
                  <Button color="primary" type="submit" size="sm">
                    {submitLoading ? (
                      <FontAwesomeIcon icon={faSpinner} className={'loader fa-spin'} />
                    ) : (
                      'Create'
                    )}
                  </Button>
                </FormGroup>
              </TabPane>
            )}
          </FormStepper>
        </form>
      ) : (
        <>
          {channel !== 'str' && hasSandbox === 1 && (
            <Sandbox ruleList={ruleList[channel]} selectedRule={ruleData} />
          )}
        </>
      )}
    </>
  );
};

RuleForm.propTypes = {
  isSandbox: PropTypes.bool,
  formData: PropTypes.object,
  channel: PropTypes.string.isRequired,
  formName: PropTypes.string.isRequired,
  moduleType: PropTypes.string.isRequired,
  hasSandbox: PropTypes.number.isRequired,
  ruleList: PropTypes.object.isRequired,
  ruleCreation: PropTypes.object.isRequired,
  ruleCreationActions: PropTypes.object.isRequired,
  fetchPrefilterLists: PropTypes.func.isRequired,
  submit: PropTypes.func.isRequired
};

export default RuleForm;
