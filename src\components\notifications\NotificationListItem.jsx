import React from 'react';
import moment from 'moment';
import { useHistory } from 'react-router-dom';
import PropTypes from 'prop-types';
import { faCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Row, Col, ListGroupItem } from 'reactstrap';

function NotificationListItem({ notification }) {
  const history = useHistory();

  return (
    <ListGroupItem
      key={notification.id}
      onClick={() => history.push(`/investigation/frm/${notification.txnId}`)}
      active={!notification.isAcknowledged}>
      <Row>
        <Col className="text-danger" md="1">
          {!notification.isAcknowledged && <FontAwesomeIcon icon={faCircle} className="fa-xs" />}
        </Col>
        <Col md="2">{notification.customerId}</Col>
        <Col md="3">{notification.message}</Col>
        <Col md="2">{moment(notification.timestamp).format('hh:mm A')}</Col>
        <Col md="2">{notification?.acknowledgedBy}</Col>
        <Col md="2">
          {notification?.acknowledgedOn
            ? moment(notification?.acknowledgedOn).format('YYYY-MM-DD HH:mm a')
            : ''}
        </Col>
      </Row>
    </ListGroupItem>
  );
}

NotificationListItem.propTypes = {
  notification: PropTypes.object.isRequired
};

export default NotificationListItem;
