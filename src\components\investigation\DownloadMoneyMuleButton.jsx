import React from 'react';
import PropTypes from 'prop-types';
import { Button } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faGavel } from '@fortawesome/free-solid-svg-icons';
import { BASE_URL, URL_CONTEXT } from 'constants/apiConstants';

const DownloadMoneyMuleButton = ({ caseRefNo, entityId, getMoneyMuleLogs }) => {
  const downloadReport = (caseRefNo, entityId) => {
    const a = document.createElement('a');
    a.download = 'MoneyMule-' + caseRefNo + '.zip';
    a.href = BASE_URL + URL_CONTEXT + `casereview/case/${caseRefNo}/merchant/${entityId}/download`;
    a.target = '_blank';
    a.rel = 'noopener noreferrer';
    a.click();
  };

  function downloadReportAndFetchLogs(caseRefNo, entityId) {
    downloadReport(caseRefNo, entityId);
    getMoneyMuleLogs(caseRefNo);
  }

  return (
    <Button
      outline
      size="sm"
      color="success"
      className="ms-1"
      title="Download MoneyMule Report"
      onClick={() => downloadReportAndFetchLogs(caseRefNo, entityId)}>
      <FontAwesomeIcon icon={faGavel} /> Download MoneyMule
    </Button>
  );
};

DownloadMoneyMuleButton.propTypes = {
  caseRefNo: PropTypes.string.isRequired,
  entityId: PropTypes.string.isRequired,
  getMoneyMuleLogs: PropTypes.func.isRequired
};

export default DownloadMoneyMuleButton;
