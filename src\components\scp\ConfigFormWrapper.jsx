import React from 'react';
import PropTypes from 'prop-types';
import { FormGroup, Label, Input, Col, Button } from 'reactstrap';

function ConfigFormWrapper({
  highlightText,
  configTitle,
  activationId,
  handleSaveConfigurations,
  handleInputChange,
  data,
  children,
  handleResetConfigurations
}) {
  return (
    <tr>
      <th className="searchable">{highlightText(configTitle)}</th>
      <td>
        <form onSubmit={(e) => handleSaveConfigurations(e)}>
          <FormGroup row>
            <Label sm={'auto'} for={activationId} className="searchable">
              {highlightText('Activate configuration')}
            </Label>
            <Col sm={2} className="pt-2">
              <Input
                type="checkbox"
                id={activationId}
                name="activation"
                value={data.activation}
                onChange={(e) => handleInputChange(e)}
                checked={data.activation === 'enabled'}
              />
            </Col>
          </FormGroup>
          {children || ''}
          <FormGroup className={`${data.isEdited ? '' : 'visibility-hide'}`} row>
            <Col sm={4} md={3} lg={2} />
            <Col sm={6} md={5} lg={4} className="no-right-padding mt-4">
              <Button size="sm" color="primary" title="Save" className="me-2" type="submit">
                Save
              </Button>
              <Button color="warning" title="Reset" size="sm" onClick={handleResetConfigurations}>
                Reset
              </Button>
            </Col>
          </FormGroup>
        </form>
      </td>
    </tr>
  );
}

ConfigFormWrapper.propTypes = {
  highlightText: PropTypes.func.isRequired,
  handleSaveConfigurations: PropTypes.func.isRequired,
  data: PropTypes.object.isRequired,
  configTitle: PropTypes.string.isRequired,
  activationId: PropTypes.string.isRequired,
  handleInputChange: PropTypes.func.isRequired,
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
    PropTypes.element
  ]),
  handleResetConfigurations: PropTypes.func.isRequired
};

export default ConfigFormWrapper;
