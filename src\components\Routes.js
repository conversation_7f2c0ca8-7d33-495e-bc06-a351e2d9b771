import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';
import { Switch, Route, useRouteMatch } from 'react-router-dom';

import AdminPageContainer from 'containers/auth/AdminPageContainer';
import PartnerBanksPageContainer from 'containers/auth/PartnerBanksPageContainer';
import AuditHomePageContainer from 'containers/audit/AuditHomePageContainer';
import AuditCaseDetailsContainer from 'containers/audit/AuditCaseDetailsContainer';
import SupervisorHomePageContainer from 'containers/supervisor/SupervisorHomePageContainer';
import SupervisorCaseDetailsContainer from 'containers/supervisor/SupervisorCaseDetailsContainer';
import SupervisorSTRCaseDetailsContainer from 'containers/supervisor/SupervisorSTRCaseDetailsContainer';
import UserManagementPageContainer from 'containers/userManagement/UserManagementPageContainer';
import MonitoringHomePageContainer from 'containers/monitoring/MonitoringHomePageContainer';
import MonitoringDetailPageContainer from 'containers/monitoring/MonitoringDetailPageContainer';
import InvestigationHomePageContainer from 'containers/investigation/InvestigationHomePageContainer';
import IndepthContainer from 'containers/investigation/IndepthContainer';
import IndepthSTRContainer from 'containers/investigation/IndepthSTRContainer';
import IndepthMoneyMuleContainer from 'containers/investigation/IndepthMoneyMuleContainer';
import RuleEngineContainer from 'containers/ruleEngine/RuleEngineContainer';
import ScpHomePageContainer from 'containers/scp/ScpHomePageContainer';
import PrefiltersMainContainer from 'containers/prefilters/PrefiltersMainContainer';
import ReleaseFundsContainer from 'containers/releaseFunds/ReleaseFundsContainer';
import AdvanceSearchPageContainer from 'containers/advanceSearch/AdvanceSearchPageContainer';
import EntityProfilingHomePageContainer from 'containers/entityProfiling/EntityProfilingHomePageContainer';
import ReportsPage from 'components/reports/ReportsPage';
import RFIReportsHomePage from 'components/rfiReports/RFIReportsHomePage';
import IncidentsHomePage from './incidents/IncidentsHomePage';
import CaseOneViewContainer from 'containers/caseReview/CaseOneViewContainer';
import AnalystDashboardContainer from 'containers/dashboards/AnalystDashboardContainer';
import SupervisorDashboardContainer from 'containers/dashboards/SupervisorDashboardContainer';
import STRDashboardContainer from 'containers/dashboards/STRDashboardContainer';
import MoneyMuleDashboardContainer from 'containers/dashboards/MoneyMuleDashboardContainer';
import SupervisorMoneyMuleCaseDetailsContainer from 'containers/supervisor/SupervisorMoneyMuleCaseDetailsContainer';
import NotificationPage from 'components/notifications/NotificationPage';
import PageNotFound from 'components/PageNotFound';
import { MODULES_LIST } from 'constants/applicationConstants';
import { isCooperative } from 'constants/publicKey';

const Routes = ({ role, channel, moduleType, loginType }) => {
  const match = useRouteMatch();
  const [screens, setScreen] = useState([]);

  useEffect(() => {
    const allScreens = _.chain(MODULES_LIST)
      .filter((module) => {
        if (!_.includes(module.role, role)) return false;
        if (
          role !== 'super-admin' &&
          role !== 'admin' &&
          _.intersection(module.channel, channel).length === 0
        )
          return false;
        if (!_.includes(module.moduleType, moduleType)) return false;
        if (_.includes(screens, module.route)) return false;
        if (isCooperative && !_.includes(module.loginType, loginType.toLowerCase())) return false;
        return true;
      })
      .map((module) => module.route)
      .uniq()
      .value();
    setScreen(allScreens);
  }, [role]);

  const dashboardScreen =
    channel[0] === 'str'
      ? STRDashboardContainer
      : channel[0] === 'moneyMule'
        ? MoneyMuleDashboardContainer
        : role === 'supervisor'
          ? SupervisorDashboardContainer
          : AnalystDashboardContainer;

  return (
    <Switch>
      {_.includes(screens, '/dashboard') && (
        <Route path={`${match.path}dashboard`} component={dashboardScreen} />
      )}
      {_.includes(screens, '/search') && (
        <Route path={`${match.path}search`} component={AdvanceSearchPageContainer} />
      )}
      {_.includes(screens, '/users') && (
        <Route path={`${match.path}users`} component={AdminPageContainer} />
      )}
      {_.includes(screens, '/banks') && (
        <Route path={`${match.path}banks`} component={PartnerBanksPageContainer} />
      )}
      {_.includes(screens, '/review') && (
        <Route path={`${match.path}review/:channel?/:txnId?`} component={CaseOneViewContainer} />
      )}
      {_.includes(screens, '/cases') && (
        <Route
          path={`${match.path}cases`}
          render={({ match: { url } }) => (
            <Switch>
              <Route exact path={`${url}/`} component={SupervisorHomePageContainer} />
              <Route path={`${url}/frm/:txnId`} component={SupervisorCaseDetailsContainer} />
              <Route path={`${url}/str/:txnId`} component={SupervisorSTRCaseDetailsContainer} />
              <Route
                path={`${url}/moneyMule/:txnId`}
                component={SupervisorMoneyMuleCaseDetailsContainer}
              />
            </Switch>
          )}
        />
      )}
      {_.includes(screens, '/employees') && (
        <Route path={`${match.path}employees`} component={UserManagementPageContainer} />
      )}
      {_.includes(screens, '/dsl') && (
        <Route path={`${match.path}dsl`} component={RuleEngineContainer} />
      )}
      {_.includes(screens, '/prefilters') && (
        <Route path={`${match.path}prefilters`} component={PrefiltersMainContainer} />
      )}
      {_.includes(screens, '/release-funds') && (
        <Route path={`${match.path}release-funds`} component={ReleaseFundsContainer} />
      )}
      {_.includes(screens, '/settings') && (
        <Route path={`${match.path}settings`} component={ScpHomePageContainer} />
      )}
      {_.includes(screens, '/investigation') && (
        <Route
          path={`${match.path}investigation`}
          render={({ match: { url } }) => (
            <Switch>
              <Route exact path={`${url}/`} component={InvestigationHomePageContainer} />
              <Route path={`${url}/frm/:txnId`} component={IndepthContainer} />
              <Route path={`${url}/str/:txnId`} component={IndepthSTRContainer} />
              <Route path={`${url}/moneyMule/:txnId`} component={IndepthMoneyMuleContainer} />
            </Switch>
          )}
        />
      )}
      {_.includes(screens, '/monitor') && (
        <Route
          path={`${match.path}monitor`}
          render={({ match: { url } }) => (
            <Switch>
              <Route exact path={`${url}/`} component={MonitoringHomePageContainer} />
              <Route path={`${url}/:channel?/:txnId`} component={MonitoringDetailPageContainer} />
            </Switch>
          )}
        />
      )}
      {_.includes(screens, '/profiling') && (
        <Route
          path={`${match.path}profiling/:type?/:value?`}
          component={EntityProfilingHomePageContainer}
        />
      )}
      {_.includes(screens, '/rfi') && (
        <Route path={`${match.path}rfi`} component={RFIReportsHomePage} />
      )}
      {_.includes(screens, '/reports') && (
        <Route path={`${match.path}reports`} component={ReportsPage} />
      )}
      {_.includes(screens, '/incidents') && (
        <Route path={`${match.path}incidents`} component={IncidentsHomePage} />
      )}
      {_.includes(screens, '/audit') && (
        <Route
          path={`${match.path}audit`}
          render={({ match: { url } }) => (
            <Switch>
              <Route exact path={`${url}/`} component={AuditHomePageContainer} />
              <Route path={`${url}/:channel?/:txnId?`} component={AuditCaseDetailsContainer} />
            </Switch>
          )}
        />
      )}
      {isCooperative && role === 'investigator' && (
        <Route path={`${match.path}notification`} component={NotificationPage} />
      )}
      <Route component={PageNotFound} />
    </Switch>
  );
};

Routes.propTypes = {
  role: PropTypes.string.isRequired,
  channel: PropTypes.array.isRequired,
  moduleType: PropTypes.string.isRequired,
  loginType: PropTypes.string.isRequired
};

export default Routes;
