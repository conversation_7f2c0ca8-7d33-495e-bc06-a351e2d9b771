import {
  ON_<PERSON>ET<PERSON>_MONEY_MULE_REPORT_MASTERS_LOADING,
  ON_FETCH_MONEY_MULE_REPORT_MASTERS_SUCCESS,
  ON_FETCH_MONEY_MULE_REPORT_MASTERS_FAILURE,
  ON_FETCH_MONEY_MULE_REPORT_DETAILS_LOADING,
  ON_FETCH_MONEY_MULE_REPORT_DETAILS_SUCCESS,
  ON_FETCH_MONEY_MULE_REPORT_DETAILS_FAILURE,
  ON_FETCH_MONEY_MULE_REPORT_LOGS_LOADING,
  ON_FETCH_MONEY_MULE_REPORT_LOGS_SUCCESS,
  ON_FETCH_MONEY_MULE_REPORT_LOGS_FAILURE,
  ON_FETCH_MONEY_MULE_REPORTS_LIST_LOADING,
  ON_FETCH_MONEY_MULE_REPORTS_LIST_SUCCESS,
  ON_<PERSON>ETCH_MONEY_MULE_REPORTS_LIST_FAILURE
} from 'constants/actionTypes';
import { onToggleLoader } from 'actions/toggleActions';
import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import { onUpdateCaseDetails } from './caseDetailsActions';
import client from 'utility/apiClient';

function fetchMoneyMuleReportMasters() {
  return client({ url: `casereview/fetch/moneyMule/case/masters` });
}

function onFetchMoneyMuleReportMastersLoading() {
  return { type: ON_FETCH_MONEY_MULE_REPORT_MASTERS_LOADING };
}

function onFetchMoneyMuleReportMastersSuccess(response) {
  return { type: ON_FETCH_MONEY_MULE_REPORT_MASTERS_SUCCESS, response };
}

function onFetchMoneyMuleReportMastersFailure(response) {
  return { type: ON_FETCH_MONEY_MULE_REPORT_MASTERS_FAILURE, response };
}

function onFetchMoneyMuleReportMasters() {
  return function (dispatch) {
    dispatch(onFetchMoneyMuleReportMastersLoading());
    return fetchMoneyMuleReportMasters().then(
      (success) => dispatch(onFetchMoneyMuleReportMastersSuccess(success)),
      (error) => dispatch(onFetchMoneyMuleReportMastersFailure(error))
    );
  };
}

function submitMoneyMuleReport(formData) {
  return client({
    method: 'POST',
    url: `casereview/moneyMule/case/suspicion/details`,
    data: formData,
    badRequestMessage: 'Unable to submit MoneyMule report request. Please check input data'
  });
}

function onSubmitMoneyMuleReport(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return submitMoneyMuleReport(formData)
      .then(
        () => {
          dispatch(onUpdateCaseDetails(formData.caseRefNo, undefined, 'moneyMule'));
          dispatch(onFetchMoneyMuleReportDetails(formData.caseRefNo));
          dispatch(onShowSuccessAlert({ message: 'Requested MoneyMule Report filing' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchMoneyMuleReportDetails(caseRefNo) {
  return client({ url: `casereview/fetch/moneyMule/case/${caseRefNo}/gos/details` });
}

function onFetchMoneyMuleReportDetailsLoading() {
  return { type: ON_FETCH_MONEY_MULE_REPORT_DETAILS_LOADING };
}

function onFetchMoneyMuleReportDetailsSuccess(response) {
  return { type: ON_FETCH_MONEY_MULE_REPORT_DETAILS_SUCCESS, response };
}

function onFetchMoneyMuleReportDetailsFailure(response) {
  return { type: ON_FETCH_MONEY_MULE_REPORT_DETAILS_FAILURE, response };
}

function onFetchMoneyMuleReportDetails(caseRefNo) {
  return function (dispatch) {
    dispatch(onFetchMoneyMuleReportDetailsLoading());
    return fetchMoneyMuleReportDetails(caseRefNo).then(
      (success) => dispatch(onFetchMoneyMuleReportDetailsSuccess(success)),
      (error) => dispatch(onFetchMoneyMuleReportDetailsFailure(error))
    );
  };
}

function fetchMoneyMuleReportLogs(caseRefNo) {
  return client({ url: `casereview/case/fetch/moneyMule/upload/logs/${caseRefNo}` });
}

function onFetchMoneyMuleReportLogsLoading() {
  return { type: ON_FETCH_MONEY_MULE_REPORT_LOGS_LOADING };
}

function onFetchMoneyMuleReportLogsSuccess(response) {
  return { type: ON_FETCH_MONEY_MULE_REPORT_LOGS_SUCCESS, response };
}

function onFetchMoneyMuleReportLogsFailure(response) {
  return { type: ON_FETCH_MONEY_MULE_REPORT_LOGS_FAILURE, response };
}

function onFetchMoneyMuleReportLogs(caseRefNo) {
  return function (dispatch) {
    dispatch(onFetchMoneyMuleReportLogsLoading());
    return fetchMoneyMuleReportLogs(caseRefNo).then(
      (success) => dispatch(onFetchMoneyMuleReportLogsSuccess(success)),
      (error) => dispatch(onFetchMoneyMuleReportLogsFailure(error))
    );
  };
}

function submitBatchId(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/fetch/moneyMule/upload/update`,
    data: formData,
    badRequestMessage: 'Unable to submit batch ID'
  });
}

function onSubmitBatchId(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return submitBatchId(formData)
      .then(
        () => {
          dispatch(onFetchMoneyMuleReportLogs(formData.caseRefNo));
          dispatch(onShowSuccessAlert({ message: 'BatchID updated successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchMoneyMuleReportsList(formData) {
  return client({ method: 'POST', url: `casereview/case/moneyMule/moneyMule/history/list`, data: formData });
}

function onFetchMoneyMuleReportsListLoading() {
  return { type: ON_FETCH_MONEY_MULE_REPORTS_LIST_LOADING };
}

function onFetchMoneyMuleReportsListSuccess(response) {
  return { type: ON_FETCH_MONEY_MULE_REPORTS_LIST_SUCCESS, response };
}

function onFetchMoneyMuleReportsListFailure(response) {
  return { type: ON_FETCH_MONEY_MULE_REPORTS_LIST_FAILURE, response };
}

function onFetchMoneyMuleReportsList(formData) {
  return function (dispatch) {
    dispatch(onFetchMoneyMuleReportsListLoading());
    return fetchMoneyMuleReportsList(formData).then(
      (success) => dispatch(onFetchMoneyMuleReportsListSuccess(success)),
      (error) => dispatch(onFetchMoneyMuleReportsListFailure(error))
    );
  };
}

export {
  onFetchMoneyMuleReportMasters,
  onSubmitMoneyMuleReport,
  onFetchMoneyMuleReportDetails,
  onFetchMoneyMuleReportLogs,
  onSubmitBatchId,
  onFetchMoneyMuleReportsList
};
