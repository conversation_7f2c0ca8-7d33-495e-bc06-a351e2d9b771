import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { FormGroup, Label, Input, Col } from 'reactstrap';
import ConfigFormWrapper from 'components/scp/ConfigFormWrapper';
import {
  inputChangeDataHandler,
  saveConfigurationsDataHandler,
  resetConfigurationsDataHandler
} from 'components/scp/scpFunctions';

function AutoAssignmentBucketSize({ highlightText, saveConfigurations, configurationsData }) {
  const [autoAssignmentBucketSize, setAutoAssignmentBucketSize] = useState({
    isEdited: false,
    ...configurationsData.configPoints
  });

  return (
    <ConfigFormWrapper
      configTitle="Auto Assignment Bucket Size"
      activationId="activationAutoAssignmentBucketSize"
      data={autoAssignmentBucketSize}
      highlightText={highlightText}
      handleSaveConfigurations={(event) =>
        saveConfigurationsDataHandler(
          event,
          configurationsData,
          autoAssignmentBucketSize,
          setAutoAssignmentBucketSize,
          saveConfigurations
        )
      }
      handleInputChange={(event) =>
        inputChangeDataHandler(event, autoAssignmentBucketSize, setAutoAssignmentBucketSize)
      }
      handleResetConfigurations={() =>
        resetConfigurationsDataHandler(configurationsData, setAutoAssignmentBucketSize)
      }>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="size" className="searchable">
          {highlightText('size:')}
        </Label>
        <Col sm={4} md={3} lg={2} className="no-right-padding">
          <Input
            type="number"
            name="size"
            id="size"
            onChange={(event) =>
              inputChangeDataHandler(event, autoAssignmentBucketSize, setAutoAssignmentBucketSize)
            }
            max={50}
            min={1}
            value={autoAssignmentBucketSize.size}
            required
          />
        </Col>
      </FormGroup>
    </ConfigFormWrapper>
  );
}

AutoAssignmentBucketSize.propTypes = {
  highlightText: PropTypes.func.isRequired,
  saveConfigurations: PropTypes.func.isRequired,
  configurationsData: PropTypes.object.isRequired
};

export default AutoAssignmentBucketSize;
