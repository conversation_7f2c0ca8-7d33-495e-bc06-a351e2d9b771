import {
  ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING,
  ON_SUCCESSFUL_SCP_CONFIGURATIONS_LIST_FETCH,
  ON_SCP_CONFIGURATIONS_LIST_FETCH_FAILURE
} from 'constants/actionTypes';
import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import client from 'utility/apiClient';

function fetchConfigurationsList() {
  return client({ url: `serviceconfigportal/config` });
}

function onFetchConfigurationsListLoading() {
  return { type: ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING };
}

function onSuccessfullFetchConfigurationsList(response) {
  return {
    type: ON_SUCCESSFUL_SCP_CONFIGURATIONS_LIST_FETCH,
    response
  };
}

function onFetchConfigurationsListFailure(response) {
  return {
    type: ON_SCP_CONFIGURATIONS_LIST_FETCH_FAILURE,
    response
  };
}

function onFetchConfigurationsList() {
  return function (dispatch) {
    dispatch(onFetchConfigurationsListLoading());
    return fetchConfigurationsList().then(
      (success) => dispatch(onSuccessfullFetchConfigurationsList(success)),
      (error) => dispatch(onFetchConfigurationsListFailure(error))
    );
  };
}

// save configurations
function saveConfigurations(formData) {
  return client({
    method: 'PUT',
    url: `serviceconfigportal/config`,
    data: formData,
    badRequestMessage: 'Unable to save configurations. Please check input data'
  });
}

function onSaveConfigurations(formData) {
  return function (dispatch) {
    return saveConfigurations(formData).then(
      () => {
        dispatch(onShowSuccessAlert({ message: 'Configurations saved successfully' }));
        dispatch(onFetchConfigurationsList());
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

export { onFetchConfigurationsList, onSaveConfigurations };
