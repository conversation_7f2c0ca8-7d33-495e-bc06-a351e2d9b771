import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { FormGroup, Label, Input, Col } from 'reactstrap';
import ConfigFormWrapper from 'components/scp/ConfigFormWrapper';
import {
  inputChangeDataHandler,
  saveConfigurationsDataHandler,
  resetConfigurationsDataHandler
} from 'components/scp/scpFunctions';

function AutoClosure({
  fraudTypes,
  highlightText,
  saveConfigurations,
  configurationsData,
  fetchFraudTypesList
}) {
  const [autoClosure, setAutoClosure] = useState({
    isEdited: false,
    ...configurationsData.configPoints
  });

  useEffect(() => {
    if (isEmpty(fraudTypes.list)) fetchFraudTypesList();
  }, []);

  const verdictOptions = fraudTypes.error
    ? null
    : fraudTypes.list.map((verdict) => (
        <option key={verdict.id} value={verdict.id}>
          {verdict.verdict}
        </option>
      ));

  const selectedVerdict =
    fraudTypes.error || autoClosure.verdictTypeId == ''
      ? null
      : fraudTypes.list.find((verdictItem) => verdictItem.id == autoClosure.verdictTypeId);

  const fraudTypeOptions =
    fraudTypes.error || isEmpty(autoClosure.verdictTypeId) || isEmpty(selectedVerdict)
      ? null
      : selectedVerdict.types.map((fraud) => (
          <option key={fraud.id} value={fraud.id}>
            {fraud.fraudName}
          </option>
        ));

  return (
    <ConfigFormWrapper
      configTitle="Auto Closure"
      activationId="activationAutoClosure"
      highlightText={highlightText}
      data={autoClosure}
      handleSaveConfigurations={(event) =>
        saveConfigurationsDataHandler(
          event,
          configurationsData,
          autoClosure,
          setAutoClosure,
          saveConfigurations
        )
      }
      handleInputChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
      handleResetConfigurations={() =>
        resetConfigurationsDataHandler(configurationsData, setAutoClosure)
      }>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="autoClosureDays" className="searchable">
          {highlightText('Auto closure in:')}
        </Label>
        <Col sm={4} md={3} lg={2} className="no-right-padding">
          <Input
            type="number"
            name="autoClosureDays"
            id="autoClosureDays"
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            max={100}
            min={0}
            value={autoClosure.autoClosureDays}
            required
          />
        </Col>
        <Label sm={2} for="autoClosureDays" className="searchable">
          {highlightText('days')}
        </Label>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="jobInterval" className="searchable">
          {highlightText('Auto closure interval:')}
        </Label>
        <Col sm={4} md={3} lg={2} className="no-right-padding">
          <Input
            type="number"
            name="jobInterval"
            id="jobInterval"
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            max={24}
            min={0}
            value={autoClosure.jobInterval}
            required
            step="any"
          />
        </Col>
        <Label sm={2} for="jobInterval" className="searchable">
          {highlightText('hrs')}
        </Label>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="timeOfDay" className="searchable">
          {highlightText('Execution time:')}
        </Label>
        <Col sm={4} md={3} lg={2} className="no-right-padding">
          <Input
            type="time"
            name="timeOfDay"
            id="timeOfDay"
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            value={autoClosure.timeOfDay}
            required
          />
        </Col>
      </FormGroup>
      <b className="p-3 my-1 row default-head">
        <span className="searchable">{highlightText('Defaults:')}</span>
      </b>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="verdictTypeId" className="searchable default-option">
          {highlightText('Verdict:')}
        </Label>
        <Col sm={6} md={5} lg={4}>
          <Input
            type="select"
            name="verdictTypeId"
            id="verdictTypeId"
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            value={autoClosure.verdictTypeId}
            required>
            <option value=""> -- select -- </option>
            {verdictOptions}
          </Input>
        </Col>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="fraudTypeId" className="searchable default-option">
          {highlightText('Fraud type:')}
        </Label>
        <Col sm={6} md={5} lg={4}>
          <Input
            type="select"
            name="fraudTypeId"
            id="fraudTypeId"
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            value={autoClosure.fraudTypeId}
            required
            disabled={autoClosure.verdictTypeId === ''}>
            <option value=""> -- select -- </option>
            {fraudTypeOptions}
          </Input>
        </Col>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="explanation" className="searchable default-option">
          {highlightText('Explanation:')}
        </Label>
        <Col sm={6} md={5} lg={4}>
          <Input
            type="textarea"
            name="explanation"
            id="explanation"
            maxLength={256}
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            value={autoClosure.explanation}
            required
            rows={4}
          />
        </Col>
      </FormGroup>
    </ConfigFormWrapper>
  );
}

AutoClosure.propTypes = {
  fraudTypes: PropTypes.object.isRequired,
  highlightText: PropTypes.func.isRequired,
  saveConfigurations: PropTypes.func.isRequired,
  fetchFraudTypesList: PropTypes.func.isRequired,
  configurationsData: PropTypes.object.isRequired
};

export default AutoClosure;
