import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchMoneyMuleReportLogs } from 'actions/moneyMuleReportActions';
import DownloadMoneyMuleButton from 'components/investigation/DownloadMoneyMuleButton';

const mapStateToProps = () => {
  return {};
};

const mapDispatchToProps = (dispatch) => {
  return {
    getMoneyMuleLogs: bindActionCreators(onFetchMoneyMuleReportLogs, dispatch)
  };
};

const DownloadMoneyMuleButtonContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(DownloadMoneyMuleButton);

export default DownloadMoneyMuleButtonContainer;
