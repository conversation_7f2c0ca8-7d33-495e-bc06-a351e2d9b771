/* eslint-disable react/prop-types */
'use strict';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { Button, FormGroup, Label, Input, TabPane, ButtonGroup } from 'reactstrap';
import CardContainer from 'components/common/CardContainer';
import ModalContainer from 'components/common/ModalContainer';
import ConfirmAlert from 'components/common/ConfirmAlert';
import Tabs from 'components/common/Tabs';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPencil, faTrash, faSpinner } from '@fortawesome/free-solid-svg-icons';
import Moment from 'moment';
import { isEmpty } from 'lodash';
import { MAX_AMOUNT } from 'constants/applicationConstants';

const MerchantLimit = ({
  toggle,
  toggleActions,
  actions,
  prefiltersList,
  currentPrefilterList,
  listType,
  role
}) => {
  const [merchantId, setMerchantId] = useState('');
  const [limitType, setLimitType] = useState('');
  const [txnAmountLimit, setTxnAmountLimit] = useState('');
  const [dailyAmountLimit, setDailyAmountLimit] = useState('');
  const [dailyCountLimit, setDailyCountLimit] = useState('');
  const [monthlyAmountLimit, setMonthlyAmountLimit] = useState('');
  const [monthlyCountLimit, setMonthlyCountLimit] = useState('');
  const [fileData, setFileData] = useState('');
  const [singleUploadLoading, setSingleUploadLoading] = useState(false);
  const [isEditmode, setIsEditmode] = useState(false);
  const [pageNo, setPageNo] = useState(0);
  const [pageRecords, setPageRecords] = useState(10);
  const [searchMerchantIdText, setSearchMerchantIdText] = useState('');
  const [remark, setRemark] = useState('');
  const [currentId, setCurrentId] = useState('');
  const [displayModal, setDisplayModal] = useState(false);
  const [downloadLimitType, setDownloadLimitType] = useState('');

  useEffect(() => {
    let formData = {
      pageNo: pageNo + 1,
      pageRecords: pageRecords
    };
    actions.onFetchLimitListWithPagination(currentPrefilterList, formData);
    actions.onFetchLimitType();
  }, []);

  const removeFilter = () => {
    let formData = {
      type: merchantId,
      keyName: 'merchantId',
      merchantlimitType: limitType,
      isWithPagination: true,
      dataKey: 'merchant'
    };

    actions.onDeleteLimitListItem(formData, currentPrefilterList, listType);
  };

  const toggleConfirmAlertModal = (data) => {
    data.merchantId && setMerchantId(data.merchantId);
    data.limitType && setLimitType(data.limitType);
    toggleActions.onToggleConfirmAlertModal(listType);
  };

  const setUpdatedValue = (filterData) => {
    setTxnAmountLimit(filterData.txnAmountLimit);
    setMonthlyAmountLimit(filterData.monthlyAmountLimit);
    setMonthlyCountLimit(filterData.monthlyCountLimit);
    setDailyCountLimit(filterData.dailyCountLimit);
    setDailyAmountLimit(filterData.dailyAmountLimit);
    setMerchantId(filterData.merchantId);
    setLimitType(filterData.limitType);
    setIsEditmode(true);
    setRemark(filterData.remark);
    setCurrentId(filterData.id);
  };

  const clearUpdatedValue = () => {
    setTxnAmountLimit('');
    setMonthlyAmountLimit('');
    setMonthlyCountLimit('');
    setDailyCountLimit('');
    setDailyAmountLimit('');
    setMerchantId('');
    setLimitType('');
    setIsEditmode(false);
    setRemark('');
    setCurrentId('');
  };

  const toggleFilterModal = (type, filterData) => {
    type == 'edit' ? setUpdatedValue(filterData) : clearUpdatedValue();

    toggleActions.onTogglePrefiltersListModal(listType);
  };

  const submitSingleEntry = (e) => {
    e.preventDefault();
    setSingleUploadLoading(true);
    let formData = {
      merchantId,
      limitType,
      ...(txnAmountLimit && { txnAmountLimit: parseFloat(txnAmountLimit) }),
      ...(monthlyAmountLimit && { monthlyAmountLimit: parseFloat(monthlyAmountLimit) }),
      ...(monthlyCountLimit && { monthlyCountLimit: parseInt(monthlyCountLimit) }),
      ...(dailyAmountLimit &&
        (limitType == 'CustomerRides' || limitType == 'NA') && {
          dailyAmountLimit: parseFloat(dailyAmountLimit)
        }),
      ...(dailyCountLimit &&
        (limitType == 'CustomerRides' || limitType == 'NA') && {
          dailyCountLimit: parseInt(dailyCountLimit)
        }),
      ...(remark && { remark })
    };

    let fetchLimitListWithPaginationData = {
      pageNo: pageNo + 1,
      pageRecords: pageRecords,
      ...(searchMerchantIdText && {
        filterCondition: {
          key: 'merchantId',
          value: searchMerchantIdText
        }
      })
    };

    if (isEditmode) {
      actions.onUpdateLimitList(
        formData,
        currentPrefilterList,
        listType,
        fetchLimitListWithPaginationData,
        currentId
      );
    } else {
      actions.onAddSingleItemToLimitList(
        formData,
        currentPrefilterList,
        listType,
        fetchLimitListWithPaginationData
      );
    }

    setSingleUploadLoading(false);
    setTxnAmountLimit('');
    setMonthlyAmountLimit('');
    setMonthlyCountLimit('');
    setDailyAmountLimit('');
    setDailyCountLimit('');
    setMerchantId('');
    setLimitType('');
  };

  const addInBulkEntity = (e) => {
    e.preventDefault();
    let formData = {
      [currentPrefilterList.prefilterValue]: fileData
    };

    let fetchLimitListWithPaginationData = {
      pageNo: pageNo + 1,
      pageRecords: pageRecords,
      ...(searchMerchantIdText && {
        filterCondition: {
          key: 'merchantId',
          value: searchMerchantIdText
        }
      })
    };

    actions.onAddItemsInBulkToLimitList(
      formData,
      currentPrefilterList,
      listType,
      fetchLimitListWithPaginationData
    );
    setFileData('');
  };

  const handlePageChange = (page) => {
    if (pageNo < page) {
      let formData = {
        pageNo: page + 1,
        pageRecords: pageRecords,
        ...(searchMerchantIdText && {
          filterCondition: {
            key: 'merchantId',
            value: searchMerchantIdText
          }
        })
      };
      actions.onFetchLimitListWithPagination(currentPrefilterList, formData);
    }

    setPageNo(page);
  };

  const handlePageSizeChange = (page, pageSize) => {
    setPageNo(page);
    setPageRecords(pageSize);
    if (pageRecords < pageSize) {
      let formData = {
        pageNo: page + 1,
        pageRecords: pageSize,
        ...(searchMerchantIdText && {
          filterCondition: {
            key: 'merchantId',
            value: searchMerchantIdText
          }
        })
      };
      actions.onFetchLimitListWithPagination(currentPrefilterList, formData);
    }
  };

  let limitTypeOption = prefiltersList.limitType.data.map((limitType, i) => (
    <option key={limitType.id + limitType.limitName + i} value={limitType.limitName}>
      {limitType.limitName}
    </option>
  ));

  const header = [
    {
      Header: 'Actions',
      minWidth: 50,
      filterable: false,
      sortable: false,
      show: role == 'checker',
      Cell: (row) => (
        <span className="d-flex justify-content-start">
          <Button
            outline
            size="sm"
            color="warning"
            title="edit"
            onClick={() => toggleFilterModal('edit', row.original)}>
            <FontAwesomeIcon icon={faPencil} />
          </Button>
          <Button
            outline
            size="sm"
            color="danger"
            title="Delete"
            className="ms-1"
            onClick={() => toggleConfirmAlertModal(row.original)}>
            <FontAwesomeIcon icon={faTrash} />
          </Button>
        </span>
      )
    },
    {
      Header: 'Merchant ID',
      accessor: 'merchantId',
      Filter: () => (
        <input
          type="text"
          value={searchMerchantIdText}
          onChange={(event) => setSearchMerchantIdText(event.target.value)}
          onKeyPress={(e) => {
            if (e.which === 13 || e.keyCode === 13) {
              let formData = {
                pageNo: 1,
                pageRecords: pageRecords,
                ...(searchMerchantIdText && {
                  filterCondition: {
                    key: 'merchantId',
                    value: searchMerchantIdText
                  }
                })
              };
              actions.onFetchLimitListWithPagination(currentPrefilterList, formData, 'search');
              setPageNo(0);
            }
          }}
        />
      )
    },
    {
      Header: 'Limit Type',
      accessor: 'limitType',
      filterable: false,
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          {limitTypeOption}
        </select>
      )
    },
    { Header: 'Daily Count Limit', accessor: 'dailyCountLimit', filterable: false },
    { Header: 'Daily Amount Limit', accessor: 'dailyAmountLimit', filterable: false },
    { Header: 'Monthly Count Limit', accessor: 'monthlyCountLimit', filterable: false },
    { Header: 'Monthly Amount Limit', accessor: 'monthlyAmountLimit', filterable: false },
    { Header: 'Transaction Amount Limit', accessor: 'txnAmountLimit', filterable: false },
    { Header: 'Remark', accessor: 'remark', filterable: false },
    { Header: 'Added by', accessor: 'addedBy', filterable: false },
    {
      Header: 'Added on',
      accessor: 'addedOn',
      minWidth: 110,
      Cell: ({ value }) => (value ? Moment(value).format('YYYY-MM-DD hh:mm A') : null),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        Moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig')),
      filterable: false
    }
  ];

  return (
    <div>
      <CardContainer
        title={currentPrefilterList.prefilterName}
        action={
          <ButtonGroup>
            <Button
              size="sm"
              color="primary"
              className="me-1"
              onClick={() => setDisplayModal(true)}>
              Download
            </Button>
            {role == 'checker' && (
              <Button size="sm" color="primary" onClick={() => toggleFilterModal('add')}>
                {`Add ${currentPrefilterList.prefilterName}`}
              </Button>
            )}
          </ButtonGroup>
        }>
        <ReactTable
          defaultFilterMethod={(filter, row) =>
            row[filter.id] &&
            row[filter.id].toString().toLowerCase().includes(filter.value.toLowerCase())
          }
          columns={header}
          data={prefiltersList.limitListWithPagination.data.merchant}
          noDataText="No data found"
          filterable
          showPaginationTop={true}
          showPaginationBottom={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          defaultPageSize={pageRecords}
          minRows={3}
          page={pageNo}
          pages={
            prefiltersList.limitListWithPagination.data.count / pageRecords > 1
              ? Math.ceil(prefiltersList.limitListWithPagination.data.count / pageRecords)
              : 1
          }
          className={'-highlight  -striped'}
          onPageChange={(page) => handlePageChange(page)}
          onPageSizeChange={(pageSize, page) => handlePageSizeChange(page, pageSize)}
          showPageJump={false}
        />
      </CardContainer>

      <ModalContainer
        size="md"
        theme={toggle.theme}
        isOpen={toggle.prefiltersListModal[listType]}
        toggle={() => toggleFilterModal('close')}
        header={`Add ${currentPrefilterList.prefilterName}`}>
        <Tabs tabNames={isEditmode ? ['Single'] : ['Single', 'Bulk']}>
          <TabPane tabId={0}>
            <form onSubmit={submitSingleEntry}>
              <FormGroup>
                <Label for="merchantId">Merchant ID</Label>
                <Input
                  type="text"
                  id="merchantId"
                  name="merchantId"
                  value={merchantId}
                  onChange={(event) => {
                    setMerchantId(event.target.value);
                  }}
                  required
                  readOnly={isEditmode}
                />
              </FormGroup>
              <FormGroup>
                <Label for="limitType">Limit Type</Label>
                <Input
                  type="select"
                  id="limitType"
                  name="limitType"
                  value={limitType}
                  onChange={(event) => {
                    setLimitType(event.target.value);
                  }}
                  required
                  disabled={isEditmode}>
                  <option value="">-- select --</option>
                  {limitTypeOption}
                </Input>
              </FormGroup>
              {(limitType == 'CustomerRides' || limitType == 'NA') && (
                <>
                  <FormGroup>
                    <Label for="dailyCountLimit">Daily Count Limit</Label>
                    <Input
                      type="number"
                      id="dailyCountLimit"
                      name="dailyCountLimit"
                      value={dailyCountLimit}
                      onChange={(event) => {
                        setDailyCountLimit(event.target.value);
                      }}
                      min={0}
                      pattern="[0-9]*"
                    />
                  </FormGroup>

                  <FormGroup>
                    <Label for="dailyAmountLimit">Daily Amount Limit</Label>
                    <Input
                      type="number"
                      id="dailyAmountLimit"
                      name="dailyAmountLimit"
                      value={dailyAmountLimit}
                      onChange={(event) => {
                        setDailyAmountLimit(event.target.value);
                      }}
                      min={0}
                      max={MAX_AMOUNT}
                      step={0.01}
                      pattern="^[0-9]*\.[0-9]{0,2}$"
                    />
                  </FormGroup>
                </>
              )}
              <FormGroup>
                <Label for="monthlyCountLimit">Monthly Count Limit</Label>
                <Input
                  type="number"
                  id="monthlyCountLimit"
                  name="monthlyCountLimit"
                  value={monthlyCountLimit}
                  onChange={(event) => {
                    setMonthlyCountLimit(event.target.value);
                  }}
                  min={0}
                  pattern="[0-9]*"
                />
              </FormGroup>
              <FormGroup>
                <Label for="monthlyAmountLimit">Monthly Amount Limit</Label>
                <Input
                  type="number"
                  id="monthlyAmountLimit"
                  name="monthlyAmountLimit"
                  value={monthlyAmountLimit}
                  onChange={(event) => {
                    setMonthlyAmountLimit(event.target.value);
                  }}
                  min={0}
                  max={MAX_AMOUNT}
                  step={0.01}
                  pattern="^[0-9]*\.[0-9]{0,2}$"
                />
              </FormGroup>
              <FormGroup>
                <Label for="txnAmountLimit">Transaction Amount Limit</Label>
                <Input
                  type="number"
                  id="txnAmountLimit"
                  name="txnAmountLimit"
                  value={txnAmountLimit}
                  onChange={(event) => {
                    setTxnAmountLimit(event.target.value);
                  }}
                  min={0}
                  max={MAX_AMOUNT}
                  step={0.01}
                  pattern="^[0-9]*\.[0-9]{0,2}$"
                />
              </FormGroup>
              <FormGroup>
                <Label for="remark">Remark</Label>
                <Input
                  type="text"
                  id="remark"
                  name="remark"
                  value={remark}
                  onChange={(event) => setRemark(event.target.value)}
                />
              </FormGroup>
              <FormGroup className="d-flex justify-content-end">
                <Button size="sm" type="submit" color="success" disabled={singleUploadLoading}>
                  {singleUploadLoading ? (
                    <FontAwesomeIcon icon={faSpinner} className={'loader fa-spin'} />
                  ) : (
                    'Save'
                  )}
                </Button>
              </FormGroup>
            </form>
          </TabPane>
          <TabPane tabId={1}>
            <form onSubmit={addInBulkEntity}>
              <FormGroup>
                <Input
                  name="fileUpload"
                  accept=".csv"
                  type="file"
                  files={fileData}
                  onChange={(event) => setFileData(event.target.files[0])}
                  required
                />
              </FormGroup>
              <FormGroup className="d-flex justify-content-end">
                <Button size="sm" type="submit" color="success" disabled={toggle.uploadLoader}>
                  Upload
                </Button>
              </FormGroup>
            </form>
          </TabPane>
        </Tabs>
      </ModalContainer>

      <ConfirmAlert
        theme={toggle.theme}
        confirmAlertModal={toggle.confirmAlertModal[listType]}
        toggleConfirmAlertModal={toggleConfirmAlertModal}
        confirmationAction={removeFilter}
        confirmAlertTitle={'Are you sure you want to delete it ?'}
      />

      <ModalContainer
        size="sm"
        theme={toggle.theme}
        isOpen={displayModal}
        toggle={() => setDisplayModal(!displayModal)}
        header={`Download Excel`}>
        <form>
          <FormGroup>
            <Input
              type="select"
              name="downloadLimitType"
              value={downloadLimitType}
              onChange={(event) => setDownloadLimitType(event.target.value)}
              required>
              <option value="" disabled>
                -- select --
              </option>
              {limitTypeOption}
            </Input>
          </FormGroup>
          <FormGroup className="d-flex justify-content-end">
            <Button
              size="sm"
              color="info"
              disabled={isEmpty(downloadLimitType)}
              onClick={() => {
                window.open(
                  `/api/v1/ifrm/listsandlimitsrdbms/limit/${downloadLimitType}/download-merchant`
                );
                setDisplayModal(false);
              }}>
              Download
            </Button>
          </FormGroup>
        </form>
      </ModalContainer>
    </div>
  );
};

MerchantLimit.propTypes = {
  toggle: PropTypes.object.isRequired,
  toggleActions: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  prefiltersList: PropTypes.object.isRequired,
  currentPrefilterList: PropTypes.object.isRequired,
  listType: PropTypes.string.isRequired,
  role: PropTypes.string.isRequired
};

export default MerchantLimit;
