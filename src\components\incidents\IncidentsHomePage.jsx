import React, { lazy, Suspense, useState } from 'react';
import { TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import Loader from 'components/loader/Loader';
import CPFIRFormContainer from 'containers/incidents/CPIFRFormContainer';
import ClosedFraudCasesTableContainer from 'containers/incidents/ClosedFraudCasesTableContainer';
import { isCooperative } from 'constants/publicKey';

const IncidentsTableContainer = lazy(() => import('containers/incidents/IncidentsTableContainer'));
const UnverifiedCasesTableContainer = lazy(() =>
  import('containers/incidents/UnverifiedCasesTableContainer')
);

function IncidentsHomePage() {
  const [visitedTab, setVisitedTab] = useState([0]);
  let tabNames = ['Closed Confirm Fraud Cases', 'Incidents'];
  if (isCooperative) tabNames.push('Unverified Cases');

  const updateVisitedTab = (tabId) =>
    !visitedTab.includes(tabId) && setVisitedTab((prev) => [...prev, tabId]);

  return (
    <div>
      <Tabs tabNames={tabNames} tabChangeAction={updateVisitedTab}>
        <TabPane tabId={0}>
          <ClosedFraudCasesTableContainer />
        </TabPane>
        <TabPane tabId={1}>
          {visitedTab.includes(1) && (
            <Suspense fallback={<Loader show={true} />}>
              <IncidentsTableContainer />
            </Suspense>
          )}
        </TabPane>
        <TabPane tabId={2}>
          {visitedTab.includes(2) && (
            <Suspense fallback={<Loader show={true} />}>
              <UnverifiedCasesTableContainer />
            </Suspense>
          )}
        </TabPane>
      </Tabs>
      <CPFIRFormContainer allowClose={visitedTab.includes(1)} />
    </div>
  );
}

export default IncidentsHomePage;
