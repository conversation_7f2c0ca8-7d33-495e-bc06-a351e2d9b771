import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onReOpenCase } from 'actions/caseAssignmentActions';
import MoneyMuleCaseReopen from 'components/common/MoneyMuleCaseReopen';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    role: state.auth.userCreds.roles,
    usersList: state.user.userslist,
    userId: state.auth.userCreds.userId,
    selectedCase: state.caseAssignment.selectedCase,
    advanceSearchData: state.advanceSearch
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    reOpenCase: bindActionCreators(onReOpenCase, dispatch)
  };
};

const MoneyMuleCaseReopenContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(MoneyMuleCaseReopen);

export default MoneyMuleCaseReopenContainer;
