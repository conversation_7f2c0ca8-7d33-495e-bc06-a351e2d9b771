import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchConfigurationsList, onSaveConfigurations } from 'actions/scpActions';
import ScpHomePage from 'components/scp/ScpHomePage';

const mapStateToProps = (state) => {
  return {
    configurationsData: state.scp.configurationsData,
    liability: state.caseAssignment.liability,
    fraudTypes: state.caseAssignment.fraudTypes,
    userRoles: state.auth.userCreds.roles
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchConfigurationsList: bindActionCreators(onFetchConfigurationsList, dispatch),
    saveConfigurations: bindActionCreators(onSaveConfigurations, dispatch)
  };
};

const ScpHomePageContainer = connect(mapStateToProps, mapDispatchToProps)(ScpHomePage);

export default ScpHomePageContainer;
