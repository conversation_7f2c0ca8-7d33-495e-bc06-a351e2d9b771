import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Card, CardBody, CardHeader, Button, Modal, ModalHeader, ModalBody, ModalFooter, FormGroup, Label, Input } from 'reactstrap';
import ReactTable from 'react-table';
import { isEmpty } from 'lodash';

import TableLoader from 'components/loader/TableLoader';

const MoneyMuleReportLog = ({ caseRefNo, moneyMuleReportLogs, getMoneyMuleReportLogs, submitBatchId }) => {
  const [showModal, setShowModal] = useState(false);
  const [batchId, setBatchId] = useState('');
  const { data, loader, error } = moneyMuleReportLogs;

  useEffect(() => {
    caseRefNo && getMoneyMuleReportLogs(caseRefNo);
  }, [caseRefNo]);

  const handleSubmitBatchId = () => {
    if (batchId.trim()) {
      submitBatchId({ caseRefNo, batchId: batchId.trim() });
      setShowModal(false);
      setBatchId('');
    }
  };

  const columns = [
    { Header: 'Batch ID', accessor: 'batchId' },
    { Header: 'File Name', accessor: 'fileName' },
    { Header: 'Status', accessor: 'status' },
    { Header: 'Created Date', accessor: 'createdDate' },
    { Header: 'Updated Date', accessor: 'updatedDate' },
    { Header: 'Remarks', accessor: 'remarks' }
  ];

  return (
    <Card>
      <CardHeader className="d-flex justify-content-between align-items-center">
        <h5 className="mb-0">MoneyMule Report Logs</h5>
        <Button color="primary" size="sm" onClick={() => setShowModal(true)}>
          Update Batch ID
        </Button>
      </CardHeader>
      <CardBody>
        {loader ? (
          <TableLoader />
        ) : error ? (
          <div className="alert alert-danger">Error loading MoneyMule report logs</div>
        ) : isEmpty(data) ? (
          <div className="text-center text-muted">No logs available</div>
        ) : (
          <ReactTable
            columns={columns}
            data={data}
            defaultPageSize={5}
            minRows={3}
            showPagination={data.length > 5}
            className="-striped -highlight"
          />
        )}
      </CardBody>

      <Modal isOpen={showModal} toggle={() => setShowModal(false)}>
        <ModalHeader toggle={() => setShowModal(false)}>Update Batch ID</ModalHeader>
        <ModalBody>
          <FormGroup>
            <Label htmlFor="batchId">Batch ID</Label>
            <Input
              type="text"
              id="batchId"
              name="batchId"
              value={batchId}
              onChange={(e) => setBatchId(e.target.value)}
              placeholder="Enter batch ID"
              required
            />
          </FormGroup>
        </ModalBody>
        <ModalFooter>
          <Button color="primary" onClick={handleSubmitBatchId} disabled={!batchId.trim()}>
            Submit
          </Button>
          <Button color="secondary" onClick={() => setShowModal(false)}>
            Cancel
          </Button>
        </ModalFooter>
      </Modal>
    </Card>
  );
};

MoneyMuleReportLog.propTypes = {
  caseRefNo: PropTypes.string.isRequired,
  moneyMuleReportLogs: PropTypes.object.isRequired,
  getMoneyMuleReportLogs: PropTypes.func.isRequired,
  submitBatchId: PropTypes.func.isRequired
};

export default MoneyMuleReportLog;
