import {
  ON_<PERSON><PERSON><PERSON>_SELECTED_CASE,
  ON_L<PERSON>BILITY_LIST_<PERSON>ETCH_FAILURE,
  ON_LIABILITY_LIST_FETCH_LOADING,
  ON_FRAUD_TYPE_LIST_FETCH_FAILURE,
  ON_FRAUD_TYPE_LIST_FETCH_LOADING,
  ON_SUCCESSFUL_LIABILITY_LIST_FETCH,
  ON_SUCCESSFUL_SUPERVISOR_APPROVAL,
  ON_SUCCESSFUL_FRAUD_TYPE_LIST_FETCH,
  ON_FETCH_PAST_INVESTIGATED_TXNS_LOADING,
  ON_FETCH_PAST_INVESTIGATED_TXNS_FAILURE,
  ON_SUCCESSFUL_FETCH_PAST_INVESTIGATED_TXNS,
  ON_BUCKETS_FETCH_LOADING,
  ON_SUCCESSFUL_BUCKETS_FETCH,
  ON_BUCKETS_FETCH_FAILURE,
  ON_CASES_FETCH_LOADING,
  ON_SUCCESSFUL_CASE<PERSON>_FETCH,
  ON_CASES_FETCH_FAILURE,
  ON_FETCH_CLOSE_CASE_BUCKETS_LOADING,
  ON_FETCH_CLOSE_CASE_BUCKETS_SUCCESS,
  ON_FETCH_CLOSE_CASE_BUCKETS_FAILURE,
  ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_LOADING,
  ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_SUCCESS,
  ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_FAILURE,
  ON_REMOVE_CASES_FROM_LIST,
  ON_FETCH_XCHANNEL_LIST_LOADING,
  ON_FETCH_XCHANNEL_LIST_SUCCESS,
  ON_FETCH_XCHANNEL_LIST_FAILURE,
  ON_FETCH_SNOOZE_CONDITIONS_LIST_LOADING,
  ON_FETCH_SNOOZE_CONDITIONS_LIST_SUCCESS,
  ON_FETCH_SNOOZE_CONDITIONS_LIST_FAILURE,
  ON_ADD_CHILD_TXNS_TO_CASE,
  ON_FETCH_TXN_TYPE_LOADING,
  ON_FETCH_TXN_TYPE_SUCCESS,
  ON_FETCH_TXN_TYPE_FAILURE
} from 'constants/actionTypes';
import { onFetchCaseDetails, onUpdateCaseDetails } from 'actions/caseDetailsActions';
import { onFetchDocumentStatus } from 'actions/releaseFundsActions';
import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import { onFetchTransactionDetails } from 'actions/transactionDetailsActions';
import {
  onToggleLoader,
  onToggleVerdictModal,
  onToggleRuleFeedbackModal
} from 'actions/toggleActions';
import client from 'utility/apiClient';
import { isCooperative } from 'constants/publicKey';
import { isEmpty } from 'lodash';
import { onRemoveCasesFromUnverifiedList } from './incidentActions';

function fetchLiabilityList() {
  return client({ url: `casereview/case/liability/type` });
}

function onFetchLiabilityListLoading() {
  return { type: ON_LIABILITY_LIST_FETCH_LOADING };
}

function onSuccessfulFetchLiabilityList(response) {
  return {
    type: ON_SUCCESSFUL_LIABILITY_LIST_FETCH,
    response
  };
}

function onFetchLiabilityListFailure(response) {
  return {
    type: ON_LIABILITY_LIST_FETCH_FAILURE,
    response
  };
}

function onFetchLiabilityList() {
  return function (dispatch) {
    dispatch(onFetchLiabilityListLoading());
    return fetchLiabilityList().then(
      (success) => dispatch(onSuccessfulFetchLiabilityList(success)),
      (error) => dispatch(onFetchLiabilityListFailure(error))
    );
  };
}

function fetchFraudTypesList() {
  return client({ url: `casereview/case/fraud/type` });
}

function onFetchFraudTypesLoading() {
  return { type: ON_FRAUD_TYPE_LIST_FETCH_LOADING };
}

function onSuccessfulFetchFraudTypesList(response) {
  return {
    type: ON_SUCCESSFUL_FRAUD_TYPE_LIST_FETCH,
    response
  };
}

function onFetchFraudTypesListFailure(response) {
  return {
    type: ON_FRAUD_TYPE_LIST_FETCH_FAILURE,
    response
  };
}

function onFetchFraudTypesList() {
  return function (dispatch) {
    dispatch(onFetchFraudTypesLoading());
    return fetchFraudTypesList().then(
      (success) => dispatch(onSuccessfulFetchFraudTypesList(success)),
      (error) => dispatch(onFetchFraudTypesListFailure(error))
    );
  };
}

function fetchPastInvestigations(formData) {
  return client({
    method: 'POST',
    url: `casereview/${formData.channel}/entityId/${formData.entityId}/case`,
    data: formData.data,
    badRequestMessage: 'Unable to get past investigated transactions of selected customer.'
  });
}

function onFetchPastInvestigationsLoading(channel) {
  return { type: ON_FETCH_PAST_INVESTIGATED_TXNS_LOADING, channel };
}

function onSuccessfulFetchPastInvestigations(
  response,
  entityId,
  channel,
  calledBy,
  filterCondition
) {
  return {
    type: ON_SUCCESSFUL_FETCH_PAST_INVESTIGATED_TXNS,
    response,
    entityId,
    channel,
    calledBy,
    filterCondition
  };
}

function onFetchPastInvestigationsFailure(response, entityId, channel) {
  return {
    type: ON_FETCH_PAST_INVESTIGATED_TXNS_FAILURE,
    response,
    entityId,
    channel
  };
}

function onFetchPastInvestigations(formData, calledBy) {
  return function (dispatch) {
    dispatch(onFetchPastInvestigationsLoading(formData.channel));
    return fetchPastInvestigations(formData).then(
      (success) =>
        dispatch(
          onSuccessfulFetchPastInvestigations(
            success,
            formData.entityId,
            formData.channel,
            calledBy,
            formData.data?.filterCondition
          )
        ),
      (error) =>
        dispatch(onFetchPastInvestigationsFailure(error, formData.entityId, formData.channel))
    );
  };
}

function updateNegativeDB(formData) {
  return client({
    method: 'POST',
    url: `frm/case/negativeDB/add`,
    data: formData,
    badRequestMessage: 'Unable to update Negative DB'
  });
}

function onUpdateNegativeDB(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return updateNegativeDB(formData)
      .then(
        () => {
          dispatch(
            onShowSuccessAlert({
              message: 'Negative DB updated'
            })
          );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function closeCase(formData, channel, type) {
  return client({
    method: 'PUT',
    url: `casereview/case/user${channel === 'str' ? '/str' : ''}/close${type}`,
    data: formData,
    badRequestMessage: 'Unable to close case. Check input data.'
  });
}

function onCloseCase(formData, channel = 'frm', type = '', negativeDBData = {}) {
  return function (dispatch, getState) {
    const selectedCase = getState().caseAssignment.selectedCase;
    const rulesWithConditions = getState().ruleConfigurator.rulesWithConditions.list;
    const pastInvestigationConfig =
      getState().caseAssignment.pastInvestigations[channel].pastInvestigationConfig;
    const data = {
      pageNo: 1,
      pageRecords: 5,
      filterCondition: pastInvestigationConfig?.filterCondition
    };
    dispatch(onToggleLoader(true));
    return closeCase(formData, channel, type == '/singleFromTable' ? '' : type)
      .then(
        (success) => {
          dispatch(onToggleVerdictModal(channel));
          dispatch(
            onShowSuccessAlert({ message: success?.message || 'Case(s) closed successfully' })
          );
          channel === 'frm' &&
            !isEmpty(negativeDBData) &&
            dispatch(onUpdateNegativeDB(negativeDBData));
          dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefNos, channel));
          channel == 'frm' &&
            type == '' &&
            !isEmpty(selectedCase.reViolatedRules) &&
            !isEmpty(rulesWithConditions) &&
            dispatch(onToggleRuleFeedbackModal());
          // re fetch latest investigation
          pastInvestigationConfig &&
            dispatch(
              onFetchPastInvestigations(
                {
                  channel: pastInvestigationConfig?.channel,
                  entityId: pastInvestigationConfig?.entityId,
                  data
                },
                'byDate'
              )
            );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function onSelectCase(selectedCase) {
  return function (dispatch, getState) {
    const { moduleType } = getState().auth;
    return dispatch(onFetchTransactionDetails(selectedCase.txnId, selectedCase.channel)).then(
      () => {
        dispatch(onFetchCaseDetails(selectedCase, selectedCase.channel));
        selectedCase.channel === 'frm' &&
          moduleType === 'acquirer' &&
          dispatch(onFetchDocumentStatus(selectedCase));
      }
    );
  };
}

function onClearSelectedCase() {
  return {
    type: ON_CLEAR_SELECTED_CASE
  };
}

function requestSupervisorApproval(formData, channel, type) {
  return client({
    method: 'POST',
    url: `casereview/case/user/approval${channel === 'str' ? '/str' : ''}${type}/checker`,
    data: formData,
    badRequestMessage: 'Unable to assign case(s) for approval.'
  });
}

function onRequestSupervisorApproval(formData, channel = 'frm', type = '') {
  return function (dispatch, getState) {
    const selectedCase = getState().caseAssignment.selectedCase;
    const rulesWithConditions = getState().ruleConfigurator.rulesWithConditions.list;
    dispatch(onToggleLoader(true));
    return requestSupervisorApproval(formData, channel, type == '/singleFromTable' ? '' : type)
      .then(
        () => {
          dispatch(onToggleVerdictModal(channel));
          dispatch(
            onShowSuccessAlert({
              message: `Successfully marked case(s) for ${
                isCooperative ? 'PO' : 'checker'
              } approval`
            })
          );
          dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefNos, channel));

          channel == 'frm' &&
            type == '' &&
            !isEmpty(selectedCase.reViolatedRules) &&
            !isEmpty(rulesWithConditions) &&
            dispatch(onToggleRuleFeedbackModal());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function supervisorApproval(formData, channel, role, type) {
  return client({
    method: 'POST',
    url: `casereview/case/user/approval${channel === 'str' ? '/str' : ''}${type}/${role}/response`,
    data: formData,
    badRequestMessage: 'Unable to provide approval.'
  });
}

function onSuccessfulSupervisorApproval(data) {
  let newStatus = data.isApproved ? 'Closed' : 'Open';
  return {
    type: ON_SUCCESSFUL_SUPERVISOR_APPROVAL,
    newStatus,
    data
  };
}

function onSupervisorApproval(formData, channel = 'frm', type = '') {
  return function (dispatch, getState) {
    const { roles } = getState().auth.userCreds;
    dispatch(onToggleLoader(true));
    return supervisorApproval(formData, channel, roles, type == '/singleFromTable' ? '' : type)
      .then(
        () => {
          dispatch(onSuccessfulSupervisorApproval(formData));
          dispatch(
            onShowSuccessAlert({
              message: formData.isApproved
                ? 'Successfully approved case(s) for closure'
                : 'Successfully marked case(s) for re-verification'
            })
          );
          dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefNos, channel));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchBuckets(role, channel) {
  return client({
    url: `casereview/case/${role}/bucket/${channel}/count`,
    badRequestMessage: 'Unable to get buckets'
  });
}

function onFetchBucketsLoading(channel) {
  return { type: ON_BUCKETS_FETCH_LOADING, channel };
}

function onSuccessfulonFetchBuckets(response, channel) {
  return {
    type: ON_SUCCESSFUL_BUCKETS_FETCH,
    response,
    channel
  };
}

function onFetchBucketsFailure(response, channel) {
  return {
    type: ON_BUCKETS_FETCH_FAILURE,
    response,
    channel
  };
}

function onFetchBuckets(role, channel) {
  return function (dispatch) {
    dispatch(onFetchBucketsLoading(channel));
    return fetchBuckets(role, channel).then(
      (success) => dispatch(onSuccessfulonFetchBuckets(success, channel)),
      (error) => dispatch(onFetchBucketsFailure(error, channel))
    );
  };
}

function fetchCases(formData, channel) {
  return client({
    method: 'POST',
    url: `casereview/case/${formData.role}/${channel}/bucket`,
    data: formData,
    badRequestMessage: 'Unable to get buckets cases'
  });
}

function onFetchCasesLoading(channel, conf) {
  return { type: ON_CASES_FETCH_LOADING, channel, conf };
}

function onSuccessfulFetchCases(response, conf, channel, refresh) {
  return {
    type: ON_SUCCESSFUL_CASES_FETCH,
    response,
    conf,
    channel,
    refresh
  };
}

function onFetchCasesFailure(response, conf, channel, refresh) {
  return {
    type: ON_CASES_FETCH_FAILURE,
    response,
    conf,
    channel,
    refresh
  };
}

function onFetchCases(conf, channel, refresh = false) {
  return function (dispatch) {
    dispatch(onFetchCasesLoading(channel, conf));
    return fetchCases(conf, channel).then(
      (success) => dispatch(onSuccessfulFetchCases(success, conf, channel, refresh)),
      (error) => dispatch(onFetchCasesFailure(error, conf, channel, refresh))
    );
  };
}

function fetchCloseCaseBuckets() {
  return client({
    url: `casereview/case/close/bucket`,
    badRequestMessage: 'Unable to fetch close case buckets'
  });
}

function onFetchCloseCaseBucketsLoading() {
  return { type: ON_FETCH_CLOSE_CASE_BUCKETS_LOADING };
}

function onSuccessfulfetchCloseCaseBuckets(response) {
  return {
    type: ON_FETCH_CLOSE_CASE_BUCKETS_SUCCESS,
    response
  };
}

function onFailureCloseCaseBucketsFetch(response) {
  return {
    type: ON_FETCH_CLOSE_CASE_BUCKETS_FAILURE,
    response
  };
}

function onFetchCloseCaseBuckets() {
  return function (dispatch) {
    dispatch(onFetchCloseCaseBucketsLoading());
    return fetchCloseCaseBuckets().then(
      (success) => dispatch(onSuccessfulfetchCloseCaseBuckets(success)),
      (error) => dispatch(onFailureCloseCaseBucketsFetch(error))
    );
  };
}

function fetchFraudTypesWithBuckets() {
  return client({
    url: `casereview/case/verdict/bucket`
  });
}

function onFetchFraudTypesWithBucketsLoading() {
  return { type: ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_LOADING };
}

function onSuccessfulfetchFraudTypesWithBuckets(response) {
  return {
    type: ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_SUCCESS,
    response
  };
}

function onFailurefetchFraudTypesWithBuckets(response) {
  return {
    type: ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_FAILURE,
    response
  };
}

function onFetchFraudTypesWithBuckets() {
  return function (dispatch) {
    dispatch(onFetchFraudTypesWithBucketsLoading());
    return fetchFraudTypesWithBuckets().then(
      (success) => dispatch(onSuccessfulfetchFraudTypesWithBuckets(success)),
      (error) => dispatch(onFailurefetchFraudTypesWithBuckets(error))
    );
  };
}

function onRemoveCasesFromList(cases, channel) {
  return {
    type: ON_REMOVE_CASES_FROM_LIST,
    cases,
    channel
  };
}

function requestPartnerApproval(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/user/approval/ext/checker`,
    data: formData,
    badRequestMessage: 'Unable to assign case(s) for approval.'
  });
}

function onRequestPartnerApproval(formData, channel = 'frm') {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return requestPartnerApproval(formData)
      .then(
        () => {
          dispatch(onToggleVerdictModal(channel));
          dispatch(
            onShowSuccessAlert({
              message: 'Successfully marked case(s) for partner approval'
            })
          );
          dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefNos, 'frm'));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchXChannelList() {
  return client({ url: `casereview/case/xchannelId/type` });
}

function onFetchXChannelListLoading() {
  return { type: ON_FETCH_XCHANNEL_LIST_LOADING };
}

function onSuccessfulFetchXChannelList(response) {
  return {
    type: ON_FETCH_XCHANNEL_LIST_SUCCESS,
    response
  };
}

function onFetchXChannelListFailure(response) {
  return {
    type: ON_FETCH_XCHANNEL_LIST_FAILURE,
    response
  };
}

function onFetchXChannelList() {
  return function (dispatch) {
    dispatch(onFetchXChannelListLoading());
    return fetchXChannelList().then(
      (success) => dispatch(onSuccessfulFetchXChannelList(success)),
      (error) => dispatch(onFetchXChannelListFailure(error))
    );
  };
}

function fetchSnoozeConditionsList() {
  return client({ url: `casereview/frm/case/auto/close/conditions` });
}

function onFetchSnoozeConditionsListLoading() {
  return { type: ON_FETCH_SNOOZE_CONDITIONS_LIST_LOADING };
}

function onFetchSnoozeConditionsListSuccess(response) {
  return {
    type: ON_FETCH_SNOOZE_CONDITIONS_LIST_SUCCESS,
    response
  };
}

function onFetchSnoozeConditionsListFailure(response) {
  return {
    type: ON_FETCH_SNOOZE_CONDITIONS_LIST_FAILURE,
    response
  };
}

function onFetchSnoozeConditionsList() {
  return function (dispatch) {
    dispatch(onFetchSnoozeConditionsListLoading());
    return fetchSnoozeConditionsList().then(
      (success) => dispatch(onFetchSnoozeConditionsListSuccess(success)),
      (error) => dispatch(onFetchSnoozeConditionsListFailure(error))
    );
  };
}

function fetchCasesFromMasterQueue() {
  return client({ url: `casereview/frm/fetch/cases/master/queue` });
}

function onFetchCasesFromMasterQueue() {
  return function (dispatch) {
    return fetchCasesFromMasterQueue().then(
      () =>
        dispatch(
          onShowSuccessAlert({
            message: 'Successfully pulled cases from master queue. Refresh after sometime.'
          })
        ),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function addRulesToCase(data) {
  return client({ method: 'PUT', url: `casereview/update/ruleids/case`, data });
}

function onAddRulesToCase(formData) {
  return function (dispatch) {
    return addRulesToCase(formData).then(
      () => {
        dispatch(onUpdateCaseDetails(formData.caseRefNo, null, 'frm'));
        dispatch(
          onShowSuccessAlert({
            message: 'Successfully added rules to case.'
          })
        );
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function addInvestigatorVerdict(formData) {
  return client({
    method: 'PUT',
    url: `casereview/investigator/update/case`,
    data: formData,
    badRequestMessage: 'Unable to update case verdict. Check input data.'
  });
}

function onAddInvestigatorVerdict(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return addInvestigatorVerdict(formData)
      .then(
        () => {
          dispatch(onToggleVerdictModal('frm'));
          dispatch(onShowSuccessAlert({ message: 'Case(s) closed successfully' }));
          dispatch(onUpdateCaseDetails(formData?.caseRefNo, null, 'frm'));
          formData?.bucketType === 'Unverified' &&
            dispatch(onRemoveCasesFromUnverifiedList(formData?.caseRefNos));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function openSTRCase(formData) {
  return client({
    method: 'POST',
    url: `uds/create/case/str`,
    data: formData,
    badRequestMessage: 'Unable to create STR case.'
  });
}

function onOpenSTRCase(formData, selectedCase = {}) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return openSTRCase(formData)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'STR case(s) create successfully' }));
          !isEmpty(selectedCase) && dispatch(onFetchCaseDetails(selectedCase, 'frm'));
          dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefNos, 'frm'));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function onAddChildTxnToCase(formData) {
  return {
    type: ON_ADD_CHILD_TXNS_TO_CASE,
    response: formData?.txnIds
  };
}

function fetchTxnTypes() {
  return client({
    url: `casereview/case/txn/type`,
    badRequestMessage: 'Transaction not found.'
  });
}

function onFetchTxnTypesLoading() {
  return { type: ON_FETCH_TXN_TYPE_LOADING };
}

function onFetchTxnTypesSuccess(response) {
  return {
    type: ON_FETCH_TXN_TYPE_SUCCESS,
    response
  };
}

function onFetchTxnTypesFailure(response) {
  return {
    type: ON_FETCH_TXN_TYPE_FAILURE,
    response
  };
}

function onFetchTxnTypes() {
  return function (dispatch) {
    dispatch(onFetchTxnTypesLoading());
    return fetchTxnTypes().then(
      (success) => dispatch(onFetchTxnTypesSuccess(success)),
      (error) => dispatch(onFetchTxnTypesFailure(error))
    );
  };
}

export {
  onFetchBuckets,
  onFetchCases,
  onFetchLiabilityList,
  onFetchFraudTypesList,
  onFetchPastInvestigations,
  onCloseCase,
  onSelectCase,
  onClearSelectedCase,
  onRequestSupervisorApproval,
  onSupervisorApproval,
  onFetchCloseCaseBuckets,
  onFetchFraudTypesWithBuckets,
  onRemoveCasesFromList,
  onRequestPartnerApproval,
  onFetchXChannelList,
  onFetchSnoozeConditionsList,
  onFetchCasesFromMasterQueue,
  onAddRulesToCase,
  onAddInvestigatorVerdict,
  onOpenSTRCase,
  onAddChildTxnToCase,
  onFetchTxnTypes
};
