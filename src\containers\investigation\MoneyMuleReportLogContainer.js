import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchMoneyMuleReportLogs, onSubmitBatchId } from 'actions/moneyMuleReportActions';
import MoneyMuleReportLog from 'components/investigation/MoneyMuleReportLog';

const mapStateToProps = (state) => {
  return {
    moneyMuleReportLogs: state.moneyMuleReport.logs
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    getMoneyMuleReportLogs: bindActionCreators(onFetchMoneyMuleReportLogs, dispatch),
    submitBatchId: bindActionCreators(onSubmitBatchId, dispatch)
  };
};

const MoneyMuleReportLogContainer = connect(mapStateToProps, mapDispatchToProps)(MoneyMuleReportLog);

export default MoneyMuleReportLogContainer;
