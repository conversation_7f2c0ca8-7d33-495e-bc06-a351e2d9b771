import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router-dom';

import MoneyMuleReportListContainer from 'containers/dashboards/MoneyMuleReportListContainer';
import { isCooperative } from 'constants/publicKey';

function MoneyMuleDashboard({ role }) {
  const history = useHistory();

  useEffect(() => {
    if (
      role !== 'principal-officer' &&
      role !== 'maker' &&
      (isCooperative ? role == 'checker' : role !== 'checker')
    )
      history.goBack();
    document.title = 'BANKiQ FRC | MoneyMule Dashboard';

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  return (
    <div className="content-wrapper">
      <MoneyMuleReportListContainer />
    </div>
  );
}

MoneyMuleDashboard.propTypes = {
  role: PropTypes.string.isRequired
};

export default MoneyMuleDashboard;
