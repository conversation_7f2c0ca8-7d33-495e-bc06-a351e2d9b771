//Auth
export const ON_SUCCESSFUL_LOGIN = 'ON_SUCCESSFUL_LOGIN';
export const ON_LOGIN_FALURE = 'ON_LOGIN_FALURE';
export const ON_SUCCESSFUL_LOGOUT = 'ON_SUCCESSFUL_LOGOUT';
export const ON_LOGOUT_FAILURE = 'ON_LOGOUT_FAILURE';
export const ON_SUCCESSFUL_CHECK_ROLE = 'ON_SUCCESSFUL_CHECK_ROLE';
export const ON_RESET_LOGIN_FORM = 'ON_RESET_LOGIN_FORM';
export const ON_TOGGLE_LOGOUT_MODAL = 'ON_TOGGLE_LOGOUT_MODAL';
export const ON_TOGGLE_SESSION_IDLE = 'ON_TOGGLE_SESSION_IDLE';
export const ON_RESET_SESSION_TIMEOUT = 'ON_RESET_SESSION_TIMEOUT';
export const ON_SUCCESSFUL_FETCH_ALL_ROLES = 'ON_SUCCESSFUL_FETCH_ALL_ROLES';
export const ON_SUCCESSFUL_FETCH_LOGIN_TYPES = 'ON_SUCCESSFUL_FETCH_LOGIN_TYPES';
export const ON_UPDATE_PASSWORD_SUCCESS = 'ON_UPDATE_PASSWORD_SUCCESS';
export const ON_2FA_AUTHENTICATE_USER_SUCCESS = 'ON_2FA_AUTHENTICATE_USER_SUCCESS';
export const ON_2FA_AUTHENTICATE_OTP_SUCCESS = 'ON_2FA_AUTHENTICATE_OTP_SUCCESS';

//User
export const ON_SUCCESSFUL_ADD_USER = 'ON_SUCCESSFUL_ADD_USER';
export const ON_SUCCESSFUL_ADD_SHIFT = 'ON_SUCCESSFUL_ADD_SHIFT';
export const ON_SUCCESSFUL_FETCH_ROLES = 'ON_SUCCESSFUL_FETCH_ROLES';
export const ON_SUCCESSFUL_FETCH_SHIFTS = 'ON_SUCCESSFUL_FETCH_SHIFTS';
export const ON_SUCCESSFUL_FETCH_STAGES = 'ON_SUCCESSFUL_FETCH_STAGES';
export const ON_SUCCESSFUL_FETCH_CHANNELS = 'ON_SUCCESSFUL_FETCH_CHANNELS';
export const ON_SUCCESSFUL_USER_LIST_FETCH = 'ON_SUCCESSFUL_USER_LIST_FETCH';
export const ON_SUCCESSFUL_UNAPPROVED_USER_LIST_FETCH = 'ON_SUCCESSFUL_UNAPPROVED_USER_LIST_FETCH';
export const ON_SUCCESSFUL_UPDATE_USER_ROLES = 'ON_SUCCESSFUL_UPDATE_USER_ROLES';
export const ON_SUCCESSFUL_USER_ASSIGN_SHIFTS = 'ON_SUCCESSFUL_USER_ASSIGN_SHIFTS';
export const ON_SUCCESSFUL_FETCH_PARTNER_ID_LIST = 'ON_SUCCESSFUL_FETCH_PARTNER_ID_LIST';
export const ON_SUCCESSFUL_FETCH_ADMIN_LIST = 'ON_SUCCESSFUL_FETCH_ADMIN_LIST';
export const ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_LOADING =
  'ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_LOADING';
export const ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_SUCCESS =
  'ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_SUCCESS';
export const ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_FAILURE =
  'ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_FAILURE';
export const ON_USER_ASSIGN_CASE_CRITERIA_SUCCESS = 'ON_USER_ASSIGN_CASE_CRITERIA_SUCCESS';
export const ON_USER_TOGGLE_AUTO_CASE_SUCCESS = 'ON_USER_TOGGLE_AUTO_CASE_SUCCESS';
export const ON_SUCCESSFUL_FETCH_PROVISIONAL_FIELDS = 'ON_SUCCESSFUL_FETCH_PROVISIONAL_FIELDS';
export const ON_FETCH_CONFIGURATIONS_SUCCESS = 'ON_FETCH_CONFIGURATIONS_SUCCESS';

//banks
export const ON_FETCH_PARTNER_BANK_LIST_LOADING = 'ON_FETCH_PARTNER_BANK_LIST_LOADING';
export const ON_FETCH_PARTNER_BANK_LIST_SUCCESS = 'ON_FETCH_PARTNER_BANK_LIST_SUCCESS';
export const ON_FETCH_PARTNER_BANK_LIST_FAILURE = 'ON_FETCH_PARTNER_BANK_LIST_FAILURE';

//Monitoring
export const ON_SUCCESSFUL_FETCH_MONITORING_DATA = 'ON_SUCCESSFUL_FETCH_MONITORING_DATA';
export const ON_SUCCESSFUL_FETCH_TRANSACTION_COUNT = 'ON_SUCCESSFUL_FETCH_TRANSACTION_COUNT';
export const ON_FETCH_MONITORING_DATA_FAILURE = 'ON_FETCH_MONITORING_DATA_FAILURE';
export const ON_FETCH_TRANSACTION_COUNT_FAILURE = 'ON_FETCH_TRANSACTION_COUNT_FAILURE';

//Alert
export const ON_SUCCESS_ALERT = 'ON_SUCCESS_ALERT';
export const ON_WARNING_ALERT = 'ON_WARNING_ALERT';
export const ON_FAILURE_ALERT = 'ON_FAILURE_ALERT';
export const ON_CLEAR_ALERT = 'ON_CLEAR_ALERT';

//Toggle
export const ON_TOGGLE_THEME = 'ON_TOGGLE_THEME';
export const ON_TOGGLE_LOADER = 'ON_TOGGLE_LOADER';
export const ON_TOGGLE_UPLOAD_LOADER = 'ON_TOGGLE_UPLOAD_LOADER';
export const ON_TOGGLE_SHIFT_MODAL = 'ON_TOGGLE_SHIFT_MODAL';
export const ON_TOGGLE_VERDICT_MODAL = 'ON_TOGGLE_VERDICT_MODAL';
export const ON_TOGGLE_DOWNLOAD_STR_MODAL = 'ON_TOGGLE_DOWNLOAD_STR_MODAL';
export const ON_TOGGLE_APPROVAL_MODAL = 'ON_TOGGLE_APPROVAL_MODAL';
export const ON_TOGGLE_CREATE_CASE_MODAL = 'ON_TOGGLE_CREATE_CASE_MODAL';
export const ON_TOGGLE_ADD_USER_MODAL = 'ON_TOGGLE_ADD_USER_MODAL';
export const ON_TOGGLE_ADD_BANK_MODAL = 'ON_TOGGLE_ADD_BANK_MODAL';
export const ON_TOGGLE_WATCHLIST_MODAL = 'ON_TOGGLE_WATCHLIST_MODAL';
export const ON_TOGGLE_BLACKLIST_MODAL = 'ON_TOGGLE_BLACKLIST_MODAL';
export const ON_TOGGLE_PREFILTERS_LIST_MODAL = 'ON_TOGGLE_PREFILTERS_LIST_MODAL';
export const ON_TOGGLE_RELEASE_FUNDS_MODAL = 'ON_TOGGLE_RELEASE_FUNDS_MODAL';
export const ON_TOGGLE_HOLD_CASE_MODAL = 'ON_TOGGLE_HOLD_CASE_MODAL';
export const ON_TOGGLE_REQUEST_DOCUMENT_MODAL = 'ON_TOGGLE_REQUEST_DOCUMENT_MODAL';
export const ON_TOGGLE_CONFIRM_ALERT_MODAL = 'ON_TOGGLE_CONFIRM_ALERT_MODAL';
export const ON_TOGGLE_PREFILTER_MODAL = 'ON_TOGGLE_PREFILTER_MODAL';
export const ON_TOGGLE_CREATE_LIST_MODAL = 'ON_TOGGLE_CREATE_LIST_MODAL';
export const ON_TOGGLE_RULE_EDIT_MODAL = 'ON_TOGGLE_RULE_EDIT_MODAL';
export const ON_TOGGLE_ESCALATION_MODAL = 'ON_TOGGLE_ESCALATION_MODAL';
export const ON_TOGGLE_STATUS_LOG_MODAL = 'ON_TOGGLE_STATUS_LOG_MODAL';
export const ON_TOGGLE_RULE_CREATE_MODAL = 'ON_TOGGLE_RULE_CREATE_MODAL';
export const ON_TOGGLE_DYNAMIC_COUNTERS_CREATE_MODAL = 'ON_TOGGLE_DYNAMIC_COUNTERS_CREATE_MODAL';
export const ON_TOGGLE_ASSIGN_SHIFT_MODAL = 'ON_TOGGLE_ASSIGN_SHIFT_MODAL';
export const ON_TOGGLE_RULE_DUPLICATE_MODAL = 'ON_TOGGLE_RULE_DUPLICATE_MODAL';
export const ON_TOGGLE_UPDATE_USER_ROLES_MODAL = 'ON_TOGGLE_UPDATE_USER_ROLES_MODAL';
export const ON_TOGGLE_ADD_TO_LIST_CONFIRM_ALERT_MODAL =
  'ON_TOGGLE_ADD_TO_LIST_CONFIRM_ALERT_MODAL';
export const ON_TOGGLE_REQUEST_FOR_INFORMATION_MODAL = 'ON_TOGGLE_REQUEST_FOR_INFORMATION_MODAL';
export const ON_TOGGLE_CPIFR_FORM_MODAL = 'ON_TOGGLE_CPIFR_FORM_MODAL';
export const ON_TOGGLE_USER_CASE_CRITERIA_MODAL = 'ON_TOGGLE_USER_CASE_CRITERIA_MODAL';
export const ON_TOGGLE_CREATE_LABEL_MODAL = 'ON_TOGGLE_CREATE_LABEL_MODAL';
export const ON_TOGGLE_RULE_FEEDBACK_MODAL = 'ON_TOGGLE_RULE_FEEDBACK_MODAL';
export const ON_TOGGLE_RESET_PASSWORD_MODAL = 'ON_TOGGLE_RESET_PASSWORD_MODAL';

//Search
export const ON_CLEAR_TRANSACTION_HISTORY_SEARCH = 'ON_CLEAR_TRANSACTION_HISTORY_SEARCH';
export const ON_TRANSACTION_HISTORY_SEARCH_LOADING = 'ON_TRANSACTION_HISTORY_SEARCH_LOADING';
export const ON_SUCCESSFUL_TRANSACTION_HISTORY_SEARCH = 'ON_SUCCESSFUL_TRANSACTION_HISTORY_SEARCH';
export const ON_TRANSACTION_HISTORY_SEARCH_FAILURE = 'ON_TRANSACTION_HISTORY_SEARCH_FAILURE';
export const ON_ADVANCE_SEARCH_TRANSACTION_LOADING = 'ON_ADVANCE_SEARCH_TRANSACTION_LOADING';
export const ON_ADVANCE_SEARCH_TRANSACTION_SUCCESS = 'ON_ADVANCE_SEARCH_TRANSACTION_SUCCESS';
export const ON_ADVANCE_SEARCH_TRANSACTION_FAILURE = 'ON_ADVANCE_SEARCH_TRANSACTION_FAILURE';
export const ON_REMOVE_SEARCH_TRANSACTION = 'ON_REMOVE_SEARCH_TRANSACTION';
export const ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_LOADING =
  'ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_LOADING';
export const ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_SUCCESS =
  'ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_SUCCESS';
export const ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_FAILURE =
  'ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_FAILURE';
export const ON_CLEAR_TRANSACTION_HISTORY_BY_STATUS = 'ON_CLEAR_TRANSACTION_HISTORY_BY_STATUS';

//Case Assignment
export const ON_LIABILITY_LIST_FETCH_LOADING = 'ON_LIABILITY_LIST_FETCH_LOADING';
export const ON_SUCCESSFUL_LIABILITY_LIST_FETCH = 'ON_SUCCESSFUL_LIABILITY_LIST_FETCH';
export const ON_LIABILITY_LIST_FETCH_FAILURE = 'ON_LIABILITY_LIST_FETCH_FAILURE';
export const ON_FRAUD_TYPE_LIST_FETCH_LOADING = 'ON_FRAUD_TYPE_LIST_FETCH_LOADING';
export const ON_SUCCESSFUL_FRAUD_TYPE_LIST_FETCH = 'ON_SUCCESSFUL_FRAUD_TYPE_LIST_FETCH';
export const ON_FRAUD_TYPE_LIST_FETCH_FAILURE = 'ON_FRAUD_TYPE_LIST_FETCH_FAILURE';
export const ON_FETCH_PAST_INVESTIGATED_TXNS_LOADING = 'ON_FETCH_PAST_INVESTIGATED_TXNS_LOADING';
export const ON_SUCCESSFUL_FETCH_PAST_INVESTIGATED_TXNS =
  'ON_SUCCESSFUL_FETCH_PAST_INVESTIGATED_TXNS';
export const ON_FETCH_PAST_INVESTIGATED_TXNS_FAILURE = 'ON_FETCH_PAST_INVESTIGATED_TXNS_FAILURE';
export const ON_CLEAR_SELECTED_CASE = 'ON_CLEAR_SELECTED_CASE';
export const ON_CASE_SELECTION = 'ON_CASE_SELECTION';
export const ON_SUCCESSFUL_CASE_CREATION = 'ON_SUCCESSFUL_CASE_CREATION';
export const ON_PRE_EXISTING_CASE = 'ON_PRE_EXISTING_CASE';
export const ON_SUCCESSFUL_SUPERVISOR_APPROVAL = 'ON_SUCCESSFUL_SUPERVISOR_APPROVAL';
export const ON_FETCH_CASE_DETAIL_LOADING = 'ON_FETCH_CASE_DETAIL_LOADING';
export const ON_FETCH_CASE_DETAIL_FAILURE = 'ON_FETCH_CASE_DETAIL_FAILURE';
export const ON_SUCCESSFUL_FETCH_CASE_DETAIL = 'ON_SUCCESSFUL_FETCH_CASE_DETAIL';
export const ON_BUCKETS_FETCH_LOADING = 'ON_BUCKETS_FETCH_LOADING';
export const ON_SUCCESSFUL_BUCKETS_FETCH = 'ON_SUCCESSFUL_BUCKETS_FETCH';
export const ON_BUCKETS_FETCH_FAILURE = 'ON_BUCKETS_FETCH_FAILURE';
export const ON_CASES_FETCH_LOADING = 'ON_CASES_FETCH_LOADING';
export const ON_SUCCESSFUL_CASES_FETCH = 'ON_SUCCESSFUL_CASES_FETCH';
export const ON_CASES_FETCH_FAILURE = 'ON_CASES_FETCH_FAILURE';
export const ON_FETCH_CLOSURE_CASES_LOADING = 'ON_FETCH_CLOSURE_CASES_LOADING';
export const ON_FETCH_CLOSURE_CASES_SUCCESS = 'ON_FETCH_CLOSURE_CASES_SUCCESS';
export const ON_FETCH_CLOSURE_CASES_FAILURE = 'ON_FETCH_CLOSURE_CASES_FAILURE';
export const ON_CLEAR_BULK_CLOSURE_SEARCH = 'ON_CLEAR_BULK_CLOSURE_SEARCH';
export const ON_FETCH_CLOSE_CASE_BUCKETS_LOADING = 'ON_FETCH_CLOSE_CASE_BUCKETS_LOADING';
export const ON_FETCH_CLOSE_CASE_BUCKETS_SUCCESS = 'ON_FETCH_CLOSE_CASE_BUCKETS_SUCCESS';
export const ON_FETCH_CLOSE_CASE_BUCKETS_FAILURE = 'ON_FETCH_CLOSE_CASE_BUCKETS_FAILURE';
export const ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_LOADING =
  'ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_LOADING';
export const ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_SUCCESS =
  'ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_SUCCESS';
export const ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_FAILURE =
  'ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_FAILURE';
export const ON_REMOVE_CASES_FROM_LIST = 'ON_REMOVE_CASES_FROM_LIST';
export const ON_FETCH_EXTERNAL_CHECKER_LIST_SUCCESS = 'ON_FETCH_EXTERNAL_CHECKER_LIST_SUCCESS';
export const ON_FETCH_EXTERNAL_CHECKER_LIST_FAILURE = 'ON_FETCH_EXTERNAL_CHECKER_LIST_FAILURE';
export const ON_FETCH_XCHANNEL_LIST_LOADING = 'ON_FETCH_XCHANNEL_LIST_LOADING';
export const ON_FETCH_XCHANNEL_LIST_SUCCESS = 'ON_FETCH_XCHANNEL_LIST_SUCCESS';
export const ON_FETCH_XCHANNEL_LIST_FAILURE = 'ON_FETCH_XCHANNEL_LIST_FAILURE';
export const ON_FETCH_SNOOZE_CONDITIONS_LIST_LOADING = 'ON_FETCH_SNOOZE_CONDITIONS_LIST_LOADING';
export const ON_FETCH_SNOOZE_CONDITIONS_LIST_SUCCESS = 'ON_FETCH_SNOOZE_CONDITIONS_LIST_SUCCESS';
export const ON_FETCH_SNOOZE_CONDITIONS_LIST_FAILURE = 'ON_FETCH_SNOOZE_CONDITIONS_LIST_FAILURE';
export const ON_ADD_CHILD_TXNS_TO_CASE = 'ON_ADD_CHILD_TXNS_TO_CASE';

//DSL
export const ON_FETCH_DSL_HELPERS_LOADING = 'ON_FETCH_DSL_HELPERS_LOADING';
export const ON_SUCCESSFUL_FETCH_DSL_HELPERS = 'ON_SUCCESSFUL_FETCH_DSL_HELPERS';
export const ON_FETCH_DSL_HELPERS_FAILURE = 'ON_FETCH_DSL_HELPERS_FAILURE';
export const ON_SUCCESSFUL_DSL_VERIFY = 'ON_SUCCESSFUL_DSL_VERIFY';
export const ON_DSL_VERIFY_FAILURE = 'ON_DSL_VERIFY_FAILURE';
export const ON_CLEAR_DSL_VALIDATION = 'ON_CLEAR_DSL_VALIDATION';
export const ON_FETCH_RULE_LIST_LOADING = 'ON_FETCH_RULE_LIST_LOADING';
export const ON_SUCCESSFUL_FETCH_RULE_LIST = 'ON_SUCCESSFUL_FETCH_RULE_LIST';
export const ON_FETCH_RULE_LIST_FAILURE = 'ON_FETCH_RULE_LIST_FAILURE';
export const ON_SUCCESSFUL_TOGGLE_RULE_STATUS = 'ON_SUCCESSFUL_TOGGLE_RULE_STATUS';
export const ON_SUCCESSFUL_TOGGLE_RULE_EXPLICIT = 'ON_SUCCESSFUL_TOGGLE_RULE_EXPLICIT';
export const ON_SUCCESSFUL_TOGGLE_RULE_ACTIVATION = 'ON_SUCCESSFUL_TOGGLE_RULE_ACTIVATION';
export const ON_SUCCESSFUL_FETCH_ACTION_LIST = 'ON_SUCCESSFUL_FETCH_ACTION_LIST';
export const ON_SUCCESSFUL_UPDATE_ACTION_LIST = 'ON_SUCCESSFUL_UPDATE_ACTION_LIST';
export const ON_SUCCESSFUL_FETCH_ALERT_CATEGORIES = 'ON_SUCCESSFUL_FETCH_ALERT_CATEGORIES';
export const ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING =
  'ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING';
export const ON_FETCH_NON_PRODUCTION_RULE_LIST_SUCCESS =
  'ON_FETCH_NON_PRODUCTION_RULE_LIST_SUCCESS';
export const ON_FETCH_NON_PRODUCTION_RULE_LIST_FAILURE =
  'ON_FETCH_NON_PRODUCTION_RULE_LIST_FAILURE';
export const ON_FETCH_RULE_NAMES_LIST_LOADING = 'ON_FETCH_RULE_NAMES_LIST_LOADING';
export const ON_FETCH_RULE_NAMES_LIST_SUCCESS = 'ON_FETCH_RULE_NAMES_LIST_SUCCESS';
export const ON_FETCH_RULE_NAMES_LIST_FAILURE = 'ON_FETCH_RULE_NAMES_LIST_FAILURE';
export const ON_FETCH_RULE_CHANNELS_LIST_SUCCESS = 'ON_FETCH_RULE_CHANNELS_LIST_SUCCESS';
export const ON_FETCH_RULE_FRAUD_CATEGORIES_LIST_SUCCESS =
  'ON_FETCH_RULE_FRAUD_CATEGORIES_LIST_SUCCESS';
export const ON_DELETE_RULE_SUCCESS = 'ON_DELETE_RULE_SUCCESS';
export const ON_FETCH_RULE_LABELS_SUCCESS = 'ON_FETCH_RULE_LABELS_SUCCESS';
export const ON_UPDATE_LABELS_ORDER_SUCCESS = 'ON_UPDATE_LABELS_ORDER_SUCCESS';
export const ON_FETCH_RULES_WITH_CONDITIONS_LOADING = 'ON_FETCH_RULES_WITH_CONDITIONS_LOADING';
export const ON_FETCH_RULES_WITH_CONDITIONS_SUCCESS = 'ON_FETCH_RULES_WITH_CONDITIONS_SUCCESS';
export const ON_FETCH_RULES_WITH_CONDITIONS_FAILURE = 'ON_FETCH_RULES_WITH_CONDITIONS_FAILURE';
export const ON_RESTORE_RULE_SUCCESS = 'ON_RESTORE_RULE_SUCCESS';
export const ON_FETCH_PREDICT_FALSE_POSITIVE_SUCCESS = 'ON_FETCH_PREDICT_FALSE_POSITIVE_SUCCESS';
export const ON_FETCH_PREDICT_FALSE_POSITIVE_FAILURE = 'ON_FETCH_PREDICT_FALSE_POSITIVE_FAILURE';
export const ON_FETCH_SIMILAR_RULE_SUCCESS = 'ON_FETCH_SIMILAR_RULE_SUCCESS';
export const ON_FETCH_SIMILAR_RULE_FAILURE = 'ON_FETCH_SIMILAR_RULE_FAILURE';

//Dynamic Counters
export const ON_FETCH_DYNAMIC_COUNTERS_LIST_LOADING = 'ON_FETCH_DYNAMIC_COUNTERS_LIST_LOADING';
export const ON_FETCH_DYNAMIC_COUNTERS_LIST_SUCCESS = 'ON_FETCH_DYNAMIC_COUNTERS_LIST_SUCCESS';
export const ON_FETCH_DYNAMIC_COUNTERS_LIST_FAILURE = 'ON_FETCH_DYNAMIC_COUNTERS_LIST_FAILURE';
export const ON_FETCH_CONDITIONAL_ATTRIBUTES_LOADING = 'ON_FETCH_CONDITIONAL_ATTRIBUTES_LOADING';
export const ON_FETCH_CONDITIONAL_ATTRIBUTES_SUCCESS = 'ON_FETCH_CONDITIONAL_ATTRIBUTES_SUCCESS';
export const ON_FETCH_CONDITIONAL_ATTRIBUTES_FAILURE = 'ON_FETCH_CONDITIONAL_ATTRIBUTES_FAILURE';
export const ON_FETCH_ALL_ATTRIBUTES_LOADING = 'ON_FETCH_ALL_ATTRIBUTES_LOADING';
export const ON_FETCH_ALL_ATTRIBUTES_SUCCESS = 'ON_FETCH_ALL_ATTRIBUTES_SUCCESS';
export const ON_FETCH_ALL_ATTRIBUTES_FAILURE = 'ON_FETCH_ALL_ATTRIBUTES_FAILURE';
export const ON_FETCH_SUB_ATTRIBUTES_LOADING = 'ON_FETCH_SUB_ATTRIBUTES_LOADING';
export const ON_FETCH_SUB_ATTRIBUTES_SUCCESS = 'ON_FETCH_SUB_ATTRIBUTES_SUCCESS';
export const ON_FETCH_SUB_ATTRIBUTES_FAILURE = 'ON_FETCH_SUB_ATTRIBUTES_FAILURE';

//Cognitive status
export const ON_FETCH_COGNITIVE_STATUS_FAILURE = 'ON_FETCH_COGNITIVE_STATUS_FAILURE';
export const ON_FETCH_COGNITIVE_STATUS_LOADING = 'ON_FETCH_COGNITIVE_STATUS_LOADING';
export const ON_SUCCESSFUL_FETCH_COGNITIVE_STATUS = 'ON_SUCCESSFUL_FETCH_COGNITIVE_STATUS';

//Violated Rules
export const ON_VIOLATED_RULES_FETCH_LOADING = 'ON_VIOLATED_RULES_FETCH_LOADING';
export const ON_SUCCESSFUL_VIOLATED_RULES_FETCH = 'ON_SUCCESSFUL_VIOLATED_RULES_FETCH';
export const ON_FAILURE_VIOLATED_RULES_FETCH = 'ON_FAILURE_VIOLATED_RULES_FETCH';
export const ON_FETCH_RULE_VIOLATION_TRANSACTIONS_LOADING =
  'ON_FETCH_RULE_VIOLATION_TRANSACTIONS_LOADING';
export const ON_FETCH_RULE_VIOLATION_TRANSACTIONS_SUCCESS =
  'ON_FETCH_RULE_VIOLATION_TRANSACTIONS_SUCCESS';
export const ON_FETCH_RULE_VIOLATION_TRANSACTIONS_FAILURE =
  'ON_FETCH_RULE_VIOLATION_TRANSACTIONS_FAILURE';

//Investigation
export const ON_CUSTOMER_TRENDS_FETCH_LOADING = 'ON_CUSTOMER_TRENDS_FETCH_LOADING';
export const ON_SUCCESSFUL_CUSTOMER_TRENDS_FETCH = 'ON_SUCCESSFUL_CUSTOMER_TRENDS_FETCH';
export const ON_CUSTOMER_TRENDS_FETCH_FAILURE = 'ON_CUSTOMER_TRENDS_FETCH_FAILURE';
export const ON_FETCH_SIMILAR_TXNS_LOADING = 'ON_FETCH_SIMILAR_TXNS_LOADING';
export const ON_SUCCESSFUL_FETCH_SIMILAR_TXNS = 'ON_SUCCESSFUL_FETCH_SIMILAR_TXNS';
export const ON_FETCH_SIMILAR_TXNS_FAILURE = 'ON_FETCH_SIMILAR_TXNS_FAILURE';
export const ON_SIMILAR_TXNS_CATEGORY_FETCH_LOADING = 'ON_SIMILAR_TXNS_CATEGORY_FETCH_LOADING';
export const ON_SIMILAR_TXNS_CATEGORY_FETCH_SUCCESS = 'ON_SIMILAR_TXNS_CATEGORY_FETCH_SUCCESS';
export const ON_SIMILAR_TXNS_CATEGORY_FETCH_FAILURE = 'ON_SIMILAR_TXNS_CATEGORY_FETCH_FAILURE';
export const ON_FETCH_CHANNEL_COUNTER_PARTY_ID_LOADING =
  'ON_FETCH_CHANNEL_COUNTER_PARTY_ID_LOADING';
export const ON_FETCH_CHANNEL_COUNTER_PARTY_ID_SUCCESS =
  'ON_FETCH_CHANNEL_COUNTER_PARTY_ID_SUCCESS';
export const ON_FETCH_CHANNEL_COUNTER_PARTY_ID_FAILURE =
  'ON_FETCH_CHANNEL_COUNTER_PARTY_ID_FAILURE';
export const ON_FETCH_TXN_TYPE_LOADING = 'ON_FETCH_TXN_TYPE_LOADING';
export const ON_FETCH_TXN_TYPE_SUCCESS = 'ON_FETCH_TXN_TYPE_SUCCESS';
export const ON_FETCH_TXN_TYPE_FAILURE = 'ON_FETCH_TXN_TYPE_FAILURE';

//History
export const ON_FETCH_TRANSACTION_HISTORY_LOADING = 'ON_FETCH_TRANSACTION_HISTORY_LOADING';
export const ON_FETCH_TRANSACTION_HISTORY_FAILURE = 'ON_FETCH_TRANSACTION_HISTORY_FAILURE';
export const ON_FETCH_SUCCESSFUL_TRANSACTION_HISTORY = 'ON_FETCH_SUCCESSFUL_TRANSACTION_HISTORY';

//Transaction Detail
export const ON_FETCH_TRANSACTION_DETAIL_LOADING = 'ON_FETCH_TRANSACTION_DETAIL_LOADING';
export const ON_SUCCESSFUL_FETCH_TRANSACTION_DETAIL = 'ON_SUCCESSFUL_FETCH_TRANSACTION_DETAIL';
export const ON_FETCH_TRANSACTION_DETAIL_FAILURE = 'ON_FETCH_TRANSACTION_DETAIL_FAILURE';

//Customer Details
export const ON_SUCCESSFUL_FETCH_CUSTOMER_VULNERABILITY_DETAILS =
  'ON_SUCCESSFUL_FETCH_CUSTOMER_VULNERABILITY_DETAILS';
export const ON_FETCH_CUSTOMER_VULNERABILITY_DETAILS_FAILURE =
  'ON_FETCH_CUSTOMER_VULNERABILITY_DETAILS_FAILURE';

//Notation
export const ON_FETCH_NOTATIONS_LIST_LOADING = 'ON_FETCH_NOTATIONS_LIST_LOADING';
export const ON_FETCH_NOTATIONS_LIST_SUCCESS = 'ON_FETCH_NOTATIONS_LIST_SUCCESS';
export const ON_FETCH_NOTATIONS_LIST_FAILURE = 'ON_FETCH_NOTATIONS_LIST_FAILURE';
export const ON_UPDATE_NOTATION_SUCCESS = 'ON_UPDATE_NOTATION_SUCCESS';
export const ON_DELETE_NOTATION_SUCCESS = 'ON_DELETE_NOTATION_SUCCESS';
export const ON_FETCH_CASE_NOTATION_LIST_LOADING = 'ON_FETCH_CASE_NOTATION_LIST_LOADING';
export const ON_FETCH_CASE_NOTATION_LIST_SUCCESS = 'ON_FETCH_CASE_NOTATION_LIST_SUCCESS';
export const ON_FETCH_CASE_NOTATION_LIST_FAILURE = 'ON_FETCH_CASE_NOTATION_LIST_FAILURE';
export const ON_ADD_CASE_NOTATION_SUCCESS = 'ON_ADD_CASE_NOTATION_SUCCESS';

//Audit
export const ON_FETCH_SELECTED_CASE_LOGS_LOADING = 'ON_FETCH_SELECTED_CASE_LOGS_LOADING';
export const ON_SUCCESSFUL_FETCH_SELECTED_CASE_LOGS = 'ON_SUCCESSFUL_FETCH_SELECTED_CASE_LOGS';
export const ON_FETCH_SELECTED_CASE_LOGS_FAILURE = 'ON_FETCH_SELECTED_CASE_LOGS_FAILURE';
export const ON_FETCH_SELECTED_ENTITY_LOGS_LOADING = 'ON_FETCH_SELECTED_ENTITY_LOGS_LOADING';
export const ON_FETCH_SELECTED_ENTITY_LOGS_SUCCESS = 'ON_FETCH_SELECTED_ENTITY_LOGS_SUCCESS';
export const ON_FETCH_SELECTED_ENTITY_LOGS_FAILURE = 'ON_FETCH_SELECTED_ENTITY_LOGS_FAILURE';

//Prefilter
export const ON_FETCH_FILTER_FAILURE = 'ON_FETCH_FILTER_FAILURE';
export const ON_FETCH_FILTER_LOADING = 'ON_FETCH_FILTER_LOADING';
export const ON_FETCH_FILTER_SUCCESS = 'ON_FETCH_FILTER_SUCCESS';
export const ON_FETCH_TPS_LIMIT_FAILURE = 'ON_FETCH_TPS_LIMIT_FAILURE';
export const ON_FETCH_TPS_LIMIT_LOADING = 'ON_FETCH_TPS_LIMIT_LOADING';
export const ON_FETCH_TPS_LIMIT_SUCCESS = 'ON_FETCH_TPS_LIMIT_SUCCESS';
export const ON_FETCH_FILTER_CATEGORY_FAILURE = 'ON_FETCH_FILTER_CATEGORY_FAILURE';
export const ON_FETCH_FILTER_CATEGORY_LOADING = 'ON_FETCH_FILTER_CATEGORY_LOADING';
export const ON_FETCH_FILTER_CATEGORY_SUCCESS = 'ON_FETCH_FILTER_CATEGORY_SUCCESS';
export const ON_ADD_TO_LIST_ITEM_CLICK = 'ON_ADD_TO_LIST_ITEM_CLICK';

//SCP
export const ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING = 'ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING';
export const ON_SUCCESSFUL_SCP_CONFIGURATIONS_LIST_FETCH =
  'ON_SUCCESSFUL_SCP_CONFIGURATIONS_LIST_FETCH';
export const ON_SCP_CONFIGURATIONS_LIST_FETCH_FAILURE = 'ON_SCP_CONFIGURATIONS_LIST_FETCH_FAILURE';

//Specialized list
export const ON_FETCH_SPECIALIZED_LIST_FAILURE = 'ON_FETCH_SPECIALIZED_LIST_FAILURE';
export const ON_FETCH_SPECIALIZED_LIST_LOADING = 'ON_FETCH_SPECIALIZED_LIST_LOADING';
export const ON_FETCH_SPECIALIZED_LIST_SUCCESS = 'ON_FETCH_SPECIALIZED_LIST_SUCCESS';
export const ON_FETCH_SPECIALIZED_LIST_CATEGORIES_FAILURE =
  'ON_FETCH_SPECIALIZED_LIST_CATEGORIES_FAILURE';
export const ON_FETCH_SPECIALIZED_LIST_CATEGORIES_LOADING =
  'ON_FETCH_SPECIALIZED_LIST_CATEGORIES_LOADING';
export const ON_FETCH_SPECIALIZED_LIST_CATEGORIES_SUCCESS =
  'ON_FETCH_SPECIALIZED_LIST_CATEGORIES_SUCCESS';
export const ON_UPDATE_SPECIALIZED_LIST_ITEM_SUCCESS = 'ON_UPDATE_SPECIALIZED_LIST_ITEM_SUCCESS';
export const ON_SUCCESSFUL_DELETE_SPECIALIZED_LIST_ITEM =
  'ON_SUCCESSFUL_DELETE_SPECIALIZED_LIST_ITEM';
export const ON_FETCH_SPECIALIZED_LIST_TYPE_FAILURE = 'ON_FETCH_SPECIALIZED_LIST_TYPE_FAILURE';
export const ON_FETCH_SPECIALIZED_LIST_TYPE_LOADING = 'ON_FETCH_SPECIALIZED_LIST_TYPE_LOADING';
export const ON_FETCH_SPECIALIZED_LIST_TYPE_SUCCESS = 'ON_FETCH_SPECIALIZED_LIST_TYPE_SUCCESS';
export const ON_FETCH_ALL_LISTS_LOADING = 'ON_FETCH_ALL_LISTS_LOADING';
export const ON_FETCH_ALL_LISTS_SUCCESS = 'ON_FETCH_ALL_LISTS_SUCCESS';
export const ON_FETCH_ALL_LISTS_FAILURE = 'ON_FETCH_ALL_LISTS_FAILURE';
export const ON_LIST_BULK_UPLOAD_INITIATED = 'ON_LIST_BULK_UPLOAD_INITIATED';
export const ON_LIST_BULK_UPLOAD_COMPLETED = 'ON_LIST_BULK_UPLOAD_COMPLETED';
export const ON_FETCH_BLOCKED_LIST_IDENTIFIER_LOADING = 'ON_FETCH_BLOCKED_LIST_IDENTIFIER_LOADING';
export const ON_FETCH_BLOCKED_LIST_IDENTIFIER_SUCCESS = 'ON_FETCH_BLOCKED_LIST_IDENTIFIER_SUCCESS';
export const ON_FETCH_BLOCKED_LIST_IDENTIFIER_FAILURE = 'ON_FETCH_BLOCKED_LIST_IDENTIFIER_FAILURE';

//Limit list
export const ON_FETCH_LIMIT_LIST_LOADING = 'ON_FETCH_LIMIT_LIST_LOADING';
export const ON_FETCH_LIMIT_LIST_SUCCESS = 'ON_FETCH_LIMIT_LIST_SUCCESS';
export const ON_FETCH_LIMIT_LIST_FAILURE = 'ON_FETCH_LIMIT_LIST_FAILURE';
export const ON_FETCH_LIMIT_TYPE_SUCCESS = 'ON_FETCH_LIMIT_TYPE_SUCCESS';
export const ON_FETCH_LIMIT_TYPE_LOADING = 'ON_FETCH_LIMIT_TYPE_LOADING';
export const ON_FETCH_LIMIT_TYPE_FAILURE = 'ON_FETCH_LIMIT_TYPE_FAILURE';
export const ON_SUCCESSFUL_DELETE_LIMIT_LIST_ITEM = 'ON_SUCCESSFUL_DELETE_LIMIT_LIST_ITEM';
export const ON_FETCH_LIMIT_LIST_WITH_PAGINATION_LOADING =
  'ON_FETCH_LIMIT_LIST_WITH_PAGINATION_LOADING';
export const ON_FETCH_LIMIT_LIST_WITH_PAGINATION_SUCCESS =
  'ON_FETCH_LIMIT_LIST_WITH_PAGINATION_SUCCESS';
export const ON_FETCH_LIMIT_LIST_WITH_PAGINATION_FAILURE =
  'ON_FETCH_LIMIT_LIST_WITH_PAGINATION_FAILURE';
export const ON_RESET_LIMIT_LIST_WITH_PAGINATION = 'ON_RESET_LIMIT_LIST_WITH_PAGINATION';

//UDS
export const ON_FETCH_UDS_ENTITY_DETAILS_LOADING = 'ON_FETCH_UDS_ENTITY_DETAILS_LOADING';
export const ON_FETCH_UDS_ENTITY_DETAILS_SUCCESS = 'ON_FETCH_UDS_ENTITY_DETAILS_SUCCESS';
export const ON_FETCH_UDS_ENTITY_DETAILS_FAILURE = 'ON_FETCH_UDS_ENTITY_DETAILS_FAILURE';
export const ON_FETCH_UDS_ENTITY_SCORES_LOADING = 'ON_FETCH_UDS_ENTITY_SCORES_LOADING';
export const ON_FETCH_UDS_ENTITY_SCORES_SUCCESS = 'ON_FETCH_UDS_ENTITY_SCORES_SUCCESS';
export const ON_FETCH_UDS_ENTITY_SCORES_FAILURE = 'ON_FETCH_UDS_ENTITY_SCORES_FAILURE';
export const ON_FETCH_FACCTUM_DETAILS_LOADING = 'ON_FETCH_FACCTUM_DETAILS_LOADING';
export const ON_FETCH_FACCTUM_DETAILS_SUCCESS = 'ON_FETCH_FACCTUM_DETAILS_SUCCESS';
export const ON_FETCH_FACCTUM_DETAILS_FAILURE = 'ON_FETCH_FACCTUM_DETAILS_FAILURE';

//Release Funds
export const ON_FETCH_RELEASE_FUNDS_LIST_LOADING = 'ON_FETCH_RELEASE_FUNDS_LIST_LOADING';
export const ON_SUCCESSFUL_FETCH_RELEASE_FUNDS_LIST = 'ON_SUCCESSFUL_FETCH_RELEASE_FUNDS_LIST';
export const ON_FETCH_RELEASE_FUNDS_LIST_FAILURE = 'ON_FETCH_RELEASE_FUNDS_LIST_FAILURE';
export const ON_FETCH_DOCUMENT_STATUS_LOADING = 'ON_FETCH_DOCUMENT_STATUS_LOADING';
export const ON_FETCH_DOCUMENT_STATUS_SUCCESS = 'ON_FETCH_DOCUMENT_STATUS_SUCCESS';
export const ON_FETCH_DOCUMENT_STATUS_FAILURE = 'ON_FETCH_DOCUMENT_STATUS_FAILURE';

//Profiling
export const ON_FETCH_ENTITY_LIST_LOADING = 'ON_FETCH_ENTITY_LIST_LOADING';
export const ON_FETCH_ENTITY_LIST_SUCCESS = 'ON_FETCH_ENTITY_LIST_SUCCESS';
export const ON_FETCH_ENTITY_LIST_FAILURE = 'ON_FETCH_ENTITY_LIST_FAILURE';
export const ON_SELECT_ENTITY = 'ON_SELECT_ENTITY';
export const ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_FAILURE =
  'ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_FAILURE';
export const ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_LOADING =
  'ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_LOADING';
export const ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_SUCCESS =
  'ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_SUCCESS';
export const ON_FETCH_PROFILING_SEARCH_CONDITIONS_LOADING =
  'ON_FETCH_PROFILING_SEARCH_CONDITIONS_LOADING';
export const ON_FETCH_PROFILING_SEARCH_CONDITIONS_SUCCESS =
  'ON_FETCH_PROFILING_SEARCH_CONDITIONS_SUCCESS';
export const ON_FETCH_PROFILING_SEARCH_CONDITIONS_FAILURE =
  'ON_FETCH_PROFILING_SEARCH_CONDITIONS_FAILURE';

//Demographic Details
export const ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_LOADING =
  'ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_LOADING';
export const ON_SUCCESSFUL_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS =
  'ON_SUCCESSFUL_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS';
export const ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_FAILURE =
  'ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_FAILURE';
export const ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_LOADING =
  'ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_LOADING';
export const ON_SUCCESSFUL_FETCH_AGENT_DEMOGRAPHIC_DETAILS =
  'ON_SUCCESSFUL_FETCH_AGENT_DEMOGRAPHIC_DETAILS';
export const ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_FAILURE =
  'ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_FAILURE';
export const ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_LOADING =
  'ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_LOADING';
export const ON_SUCCESSFUL_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS =
  'ON_SUCCESSFUL_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS';
export const ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_FAILURE =
  'ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_FAILURE';

//Statistics details
export const ON_FETCH_TRANSACTION_STATISTICS_DETAILS_LOADING =
  'ON_FETCH_TRANSACTION_STATISTICS_DETAILS_LOADING';
export const ON_SUCCESSFUL_FETCH_TRANSACTION_STATISTICS_DETAILS =
  'ON_SUCCESSFUL_FETCH_TRANSACTION_STATISTICS_DETAILS';
export const ON_FETCH_TRANSACTION_STATISTICS_DETAILS_FAILURE =
  'ON_FETCH_TRANSACTION_STATISTICS_DETAILS_FAILURE';

export const ON_FETCH_CUSTOMER_STATISTICS_DETAILS_LOADING =
  'ON_FETCH_CUSTOMER_STATISTICS_DETAILS_LOADING';
export const ON_SUCCESSFUL_FETCH_CUSTOMER_STATISTICS_DETAILS =
  'ON_SUCCESSFUL_FETCH_CUSTOMER_STATISTICS_DETAILS';
export const ON_FETCH_CUSTOMER_STATISTICS_DETAILS_FAILURE =
  'ON_FETCH_CUSTOMER_STATISTICS_DETAILS_FAILURE';

//Sandbox
export const ON_FETCH_SANDBOX_DATE_RANGE_LOADING = 'ON_FETCH_SANDBOX_DATE_RANGE_LOADING';
export const ON_FETCH_SANDBOX_DATE_RANGE_SUCCESS = 'ON_FETCH_SANDBOX_DATE_RANGE_SUCCESS';
export const ON_FETCH_SANDBOX_DATE_RANGE_FAILURE = 'ON_FETCH_SANDBOX_DATE_RANGE_FAILURE';
export const ON_TEST_SANDBOX_RULES_LOADING = 'ON_TEST_SANDBOX_RULES_LOADING';
export const ON_TEST_SANDBOX_RULES_SUCCESS = 'ON_TEST_SANDBOX_RULES_SUCCESS';
export const ON_TEST_SANDBOX_RULES_FAILURE = 'ON_FTEST_SANDBOX_RULES_FAILURE';
export const ON_FETCH_SANDBOX_STATUS_LOADING = 'ON_FETCH_SANDBOX_STATUS_LOADING';
export const ON_FETCH_SANDBOX_STATUS_SUCCESS = 'ON_FETCH_SANDBOX_STATUS_SUCCESS';
export const ON_FETCH_SANDBOX_STATUS_FAILURE = 'ON_FETCH_SANDBOX_STATUS_FAILURE';
export const ON_FETCH_SANDBOX_VIOLATION_DETAILS_LOADING =
  'ON_FETCH_SANDBOX_VIOLATION_DETAILS_LOADING';
export const ON_FETCH_SANDBOX_VIOLATION_DETAILS_SUCCESS =
  'ON_FETCH_SANDBOX_VIOLATION_DETAILS_SUCCESS';
export const ON_FETCH_SANDBOX_VIOLATION_DETAILS_FAILURE =
  'ON_FETCH_SANDBOX_VIOLATION_DETAILS_FAILURE';
export const ON_FETCH_SANDBOX_HISTORY_LOADING = 'ON_FETCH_SANDBOX_HISTORY_LOADING';
export const ON_FETCH_SANDBOX_HISTORY_SUCCESS = 'ON_FETCH_SANDBOX_HISTORY_SUCCESS';
export const ON_FETCH_SANDBOX_HISTORY_FAILURE = 'ON_FETCH_SANDBOX_HISTORY_FAILURE';

//Snooze Rule
export const ON_FETCH_SNOOZE_RULE_LIST_LOADING = 'ON_FETCH_SNOOZE_RULE_LIST_LOADING';
export const ON_FETCH_SNOOZE_RULE_LIST_SUCCESS = 'ON_FETCH_SNOOZE_RULE_LIST_SUCCESS';
export const ON_FETCH_SNOOZE_RULE_LIST_FAILURE = 'ON_FETCH_SNOOZE_RULE_LIST_FAILURE';
export const ON_FETCH_SNOOZE_ATTRIBUTES_LOADING = 'ON_FETCH_SNOOZE_ATTRIBUTES_LOADING';
export const ON_FETCH_SNOOZE_ATTRIBUTES_SUCCESS = 'ON_FETCH_SNOOZE_ATTRIBUTES_SUCCESS';
export const ON_FETCH_SNOOZE_ATTRIBUTES_FAILURE = 'ON_FETCH_SNOOZE_ATTRIBUTES_FAILURE';

//Citation
export const ON_FETCH_CASE_CITATION_LOADING = 'ON_FETCH_CASE_CITATION_LOADING';
export const ON_FETCH_CASE_CITATION_SUCCESS = 'ON_FETCH_CASE_CITATION_SUCCESS';
export const ON_FETCH_CASE_CITATION_FAILURE = 'ON_FETCH_CASE_CITATION_FAILURE';
export const ON_ADD_CITATION_COMMENT_LOADING = 'ON_ADD_CITATION_COMMENT_LOADING';
export const ON_ADD_CITATION_COMMENT_SUCCESS = 'ON_ADD_CITATION_COMMENT_SUCCESS';
export const ON_ADD_CITATION_COMMENT_FAILURE = 'ON_ADD_CITATION_COMMENT_FAILURE';
export const ON_SUCCESSFUL_FETCH_CHECKLIST_OPTIONS = 'ON_SUCCESSFUL_FETCH_CHECKLIST_OPTIONS';
export const ON_SUCCESSFUL_FETCH_CHECKLIST = 'ON_SUCCESSFUL_FETCH_CHECKLIST';
export const ON_FETCH_CHECKLIST_OPTIONS_FAILURE = 'ON_FETCH_CHECKLIST_OPTIONS_FAILURE';
export const ON_FETCH_CHECKLIST_FAILURE = 'ON_FETCH_CHECKLIST_FAILURE';

//STR XML
export const ON_FETCH_STR_REPORT_MASTERS_LOADING = 'ON_FETCH_STR_REPORT_MASTERS_LOADING';
export const ON_FETCH_STR_REPORT_MASTERS_SUCCESS = 'ON_FETCH_STR_REPORT_MASTERS_SUCCESS';
export const ON_FETCH_STR_REPORT_MASTERS_FAILURE = 'ON_FETCH_STR_REPORT_MASTERS_FAILURE';
export const ON_FETCH_STR_REPORT_DETAILS_LOADING = 'ON_FETCH_STR_REPORT_DETAILS_LOADING';
export const ON_FETCH_STR_REPORT_DETAILS_SUCCESS = 'ON_FETCH_STR_REPORT_DETAILS_SUCCESS';
export const ON_FETCH_STR_REPORT_DETAILS_FAILURE = 'ON_FETCH_STR_REPORT_DETAILS_FAILURE';
export const ON_FETCH_STR_REPORT_LOGS_LOADING = 'ON_FETCH_STR_REPORT_LOGS_LOADING';
export const ON_FETCH_STR_REPORT_LOGS_SUCCESS = 'ON_FETCH_STR_REPORT_LOGS_SUCCESS';
export const ON_FETCH_STR_REPORT_LOGS_FAILURE = 'ON_FETCH_STR_REPORT_LOGS_FAILURE';
export const ON_FETCH_STR_REPORTS_LIST_LOADING = 'ON_FETCH_STR_REPORTS_LIST_LOADING';
export const ON_FETCH_STR_REPORTS_LIST_SUCCESS = 'ON_FETCH_STR_REPORTS_LIST_SUCCESS';
export const ON_FETCH_STR_REPORTS_LIST_FAILURE = 'ON_FETCH_STR_REPORTS_LIST_FAILURE';

//Settings
export const ON_FETCH_SETTINGS_LOADING = 'ON_FETCH_SETTINGS_LOADING';
export const ON_FETCH_SETTINGS_SUCCESS = 'ON_FETCH_SETTINGS_SUCCESS';
export const ON_FETCH_SETTINGS_FAILURE = 'ON_FETCH_SETTINGS_FAILURE';

//Case Documents
export const ON_FETCH_CASE_DOCUMENTS_LOADING = 'ON_FETCH_CASE_DOCUMENTS_LOADING';
export const ON_FETCH_CASE_DOCUMENTS_SUCCESS = 'ON_FETCH_CASE_DOCUMENTS_SUCCESS';
export const ON_FETCH_CASE_DOCUMENTS_FAILURE = 'ON_FETCH_CASE_DOCUMENTS_FAILURE';

//RFI reports
export const ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_LOADING =
  'ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_LOADING';
export const ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_SUCCESS =
  'ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_SUCCESS';
export const ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_FAILURE =
  'ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_FAILURE';
export const ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_LOADING =
  'ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_LOADING';
export const ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_SUCCESS =
  'ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_SUCCESS';
export const ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_FAILURE =
  'ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_FAILURE';
export const ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_LOADING =
  'ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_LOADING';
export const ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_SUCCESS =
  'ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_SUCCESS';
export const ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_FAILURE =
  'ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_FAILURE';
export const ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_LOADING =
  'ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_LOADING';
export const ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_SUCCESS =
  'ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_SUCCESS';
export const ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_FAILURE =
  'ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_FAILURE';
export const ON_FETCH_TOP_MERCHANT_COUNT_LOADING = 'ON_FETCH_TOP_MERCHANT_COUNT_LOADING';
export const ON_FETCH_TOP_MERCHANT_COUNT_SUCCESS = 'ON_FETCH_TOP_MERCHANT_COUNT_SUCCESS';
export const ON_FETCH_TOP_MERCHANT_COUNT_FAILURE = 'ON_FETCH_TOP_MERCHANT_COUNT_FAILURE';
export const ON_FETCH_TOP_MERCHANT_DATA_LOADING = 'ON_FETCH_TOP_MERCHANT_DATA_LOADING';
export const ON_FETCH_TOP_MERCHANT_DATA_SUCCESS = 'ON_FETCH_TOP_MERCHANT_DATA_SUCCESS';
export const ON_FETCH_TOP_MERCHANT_DATA_FAILURE = 'ON_FETCH_TOP_MERCHANT_DATA_FAILURE';
export const ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_LOADING =
  'ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_LOADING';
export const ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_SUCCESS =
  'ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_SUCCESS';
export const ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_FAILURE =
  'ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_FAILURE';
export const ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_LOADING =
  'ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_LOADING';
export const ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_SUCCESS =
  'ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_SUCCESS';
export const ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_FAILURE =
  'ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_FAILURE';
export const ON_FETCH_NBFC_TRXNS_COUNT_LOADING = 'ON_FETCH_NBFC_TRXNS_COUNT_LOADING';
export const ON_FETCH_NBFC_TRXNS_COUNT_SUCCESS = 'ON_FETCH_NBFC_TRXNS_COUNT_SUCCESS';
export const ON_FETCH_NBFC_TRXNS_COUNT_FAILURE = 'ON_FETCH_NBFC_TRXNS_COUNT_FAILURE';
export const ON_FETCH_NBFC_TRXNS_DATA_LOADING = 'ON_FETCH_NBFC_TRXNS_DATA_LOADING';
export const ON_FETCH_NBFC_TRXNS_DATA_SUCCESS = 'ON_FETCH_NBFC_TRXNS_DATA_SUCCESS';
export const ON_FETCH_NBFC_TRXNS_DATA_FAILURE = 'ON_FETCH_NBFC_TRXNS_DATA_FAILURE';
export const ON_FETCH_HRC_TRXNS_COUNT_LOADING = 'ON_FETCH_HRC_TRXNS_COUNT_LOADING';
export const ON_FETCH_HRC_TRXNS_COUNT_SUCCESS = 'ON_FETCH_HRC_TRXNS_COUNT_SUCCESS';
export const ON_FETCH_HRC_TRXNS_COUNT_FAILURE = 'ON_FETCH_HRC_TRXNS_COUNT_FAILURE';
export const ON_FETCH_HRC_TRXNS_DATA_LOADING = 'ON_FETCH_HRC_TRXNS_DATA_LOADING';
export const ON_FETCH_HRC_TRXNS_DATA_SUCCESS = 'ON_FETCH_HRC_TRXNS_DATA_SUCCESS';
export const ON_FETCH_HRC_TRXNS_DATA_FAILURE = 'ON_FETCH_HRC_TRXNS_DATA_FAILURE';
export const ON_FETCH_UNUSUAL_DECLINE_COUNT_LOADING = 'ON_FETCH_UNUSUAL_DECLINE_COUNT_LOADING';
export const ON_FETCH_UNUSUAL_DECLINE_COUNT_SUCCESS = 'ON_FETCH_UNUSUAL_DECLINE_COUNT_SUCCESS';
export const ON_FETCH_UNUSUAL_DECLINE_COUNT_FAILURE = 'ON_FETCH_UNUSUAL_DECLINE_COUNT_FAILURE';
export const ON_FETCH_UNUSUAL_DECLINE_DATA_LOADING = 'ON_FETCH_UNUSUAL_DECLINE_DATA_LOADING';
export const ON_FETCH_UNUSUAL_DECLINE_DATA_SUCCESS = 'ON_FETCH_UNUSUAL_DECLINE_DATA_SUCCESS';
export const ON_FETCH_UNUSUAL_DECLINE_DATA_FAILURE = 'ON_FETCH_UNUSUAL_DECLINE_DATA_FAILURE';

//Incident report
export const ON_FETCH_ALL_INCIDENTS_LOADING = 'ON_FETCH_ALL_INCIDENTS_LOADING';
export const ON_FETCH_ALL_INCIDENTS_SUCCESS = 'ON_FETCH_ALL_INCIDENTS_SUCCESS';
export const ON_FETCH_ALL_INCIDENTS_FAILURE = 'ON_FETCH_ALL_INCIDENTS_FAILURE';
export const ON_FETCH_INCIDENT_BY_ID_LOADING = 'ON_FETCH_INCIDENT_BY_ID_LOADING';
export const ON_FETCH_INCIDENT_BY_ID_SUCCESS = 'ON_FETCH_INCIDENT_BY_ID_SUCCESS';
export const ON_FETCH_INCIDENT_BY_ID_FAILURE = 'ON_FETCH_INCIDENT_BY_ID_FAILURE';
export const ON_FETCH_CPIFR_MASTERS_LOADING = 'ON_FETCH_CPIFR_MASTERS_LOADING';
export const ON_FETCH_CPIFR_MASTERS_SUCCESS = 'ON_FETCH_CPIFR_MASTERS_SUCCESS';
export const ON_FETCH_CPIFR_MASTERS_FAILURE = 'ON_FETCH_CPIFR_MASTERS_FAILURE';
export const ON_FETCH_CASE_CLOSED_CPIFR_LOADING = 'ON_FETCH_CASE_CLOSED_CPIFR_LOADING';
export const ON_FETCH_CASE_CLOSED_CPIFR_SUCCESS = 'ON_FETCH_CASE_CLOSED_CPIFR_SUCCESS';
export const ON_FETCH_CASE_CLOSED_CPIFR_FAILURE = 'ON_FETCH_CASE_CLOSED_CPIFR_FAILURE';
export const ON_FETCH_CPIFR_REPORT_HISTORY_LOADING = 'ON_FETCH_CPIFR_REPORT_HISTORY_LOADING';
export const ON_FETCH_CPIFR_REPORT_HISTORY_SUCCESS = 'ON_FETCH_CPIFR_REPORT_HISTORY_SUCCESS';
export const ON_FETCH_CPIFR_REPORT_HISTORY_FAILURE = 'ON_FETCH_CPIFR_REPORT_HISTORY_FAILURE';
export const ON_CLEAR_SELECTED_INCIDENT = 'ON_CLEAR_SELECTED_INCIDENT';
export const ON_FETCH_UNVERIFIED_CASES_LOADING = 'ON_FETCH_UNVERIFIED_CASES_LOADING';
export const ON_FETCH_UNVERIFIED_CASES_SUCCESS = 'ON_FETCH_UNVERIFIED_CASES_SUCCESS';
export const ON_FETCH_UNVERIFIED_CASES_FAILURE = 'ON_FETCH_UNVERIFIED_CASES_FAILURE';
export const ON_REMOVE_CASES_FROM_UNVERIFIED_LIST = 'ON_REMOVE_CASES_FROM_UNVERIFIED_LIST';

//SLA Dashboard
export const ON_FETCH_SLA_KPI_LOADING = 'ON_FETCH_SLA_KPI_LOADING';
export const ON_FETCH_SLA_KPI_SUCCESS = 'ON_FETCH_SLA_KPI_SUCCESS';
export const ON_FETCH_SLA_KPI_FAILURE = 'ON_FETCH_SLA_KPI_FAILURE';
export const ON_FETCH_ANALYST_TAT_LOADING = 'ON_FETCH_ANALYST_TAT_LOADING';
export const ON_FETCH_ANALYST_TAT_SUCCESS = 'ON_FETCH_ANALYST_TAT_SUCCESS';
export const ON_FETCH_ANALYST_TAT_FAILURE = 'ON_FETCH_ANALYST_TAT_FAILURE';
export const ON_FETCH_EMPLOYEE_SLA_LOADING = 'ON_FETCH_EMPLOYEE_SLA_LOADING';
export const ON_FETCH_EMPLOYEE_SLA_SUCCESS = 'ON_FETCH_EMPLOYEE_SLA_SUCCESS';
export const ON_FETCH_EMPLOYEE_SLA_FAILURE = 'ON_FETCH_EMPLOYEE_SLA_FAILURE';
export const ON_FETCH_SHIFT_DETAILS_LOADING = 'ON_FETCH_SHIFT_DETAILS_LOADING';
export const ON_FETCH_SHIFT_DETAILS_SUCCESS = 'ON_FETCH_SHIFT_DETAILS_SUCCESS';
export const ON_FETCH_SHIFT_DETAILS_FAILURE = 'ON_FETCH_SHIFT_DETAILS_FAILURE';
export const ON_FETCH_OCCUPANCY_RATE_LOADING = 'ON_FETCH_OCCUPANCY_RATE_LOADING';
export const ON_FETCH_OCCUPANCY_RATE_SUCCESS = 'ON_FETCH_OCCUPANCY_RATE_SUCCESS';
export const ON_FETCH_OCCUPANCY_RATE_FAILURE = 'ON_FETCH_OCCUPANCY_RATE_FAILURE';
export const ON_FETCH_SLA_BREACH_CASES_LOADING = 'ON_FETCH_SLA_BREACH_CASES_LOADING';
export const ON_FETCH_SLA_BREACH_CASES_SUCCESS = 'ON_FETCH_SLA_BREACH_CASES_SUCCESS';
export const ON_FETCH_SLA_BREACH_CASES_FAILURE = 'ON_FETCH_SLA_BREACH_CASES_FAILURE';
export const ON_FETCH_FIRST_CONTACT_RATE_LOADING = 'ON_FETCH_FIRST_CONTACT_RATE_LOADING';
export const ON_FETCH_FIRST_CONTACT_RATE_SUCCESS = 'ON_FETCH_FIRST_CONTACT_RATE_SUCCESS';
export const ON_FETCH_FIRST_CONTACT_RATE_FAILURE = 'ON_FETCH_FIRST_CONTACT_RATE_FAILURE';
export const ON_FETCH_PARTNERS_CASE_STATS_LOADING = 'ON_FETCH_PARTNERS_CASE_STATS_LOADING';
export const ON_FETCH_PARTNERS_CASE_STATS_SUCCESS = 'ON_FETCH_PARTNERS_CASE_STATS_SUCCESS';
export const ON_FETCH_PARTNERS_CASE_STATS_FAILURE = 'ON_FETCH_PARTNERS_CASE_STATS_FAILURE';

//Business Dashboard
export const ON_FETCH_BUSINESS_KPI_LOADING = 'ON_FETCH_BUSINESS_KPI_LOADING';
export const ON_FETCH_BUSINESS_KPI_SUCCESS = 'ON_FETCH_BUSINESS_KPI_SUCCESS';
export const ON_FETCH_BUSINESS_KPI_FAILURE = 'ON_FETCH_BUSINESS_KPI_FAILURE';
export const ON_FETCH_ACTIONS_SHARE_LOADING = 'ON_FETCH_ACTIONS_SHARE_LOADING';
export const ON_FETCH_ACTIONS_SHARE_SUCCESS = 'ON_FETCH_ACTIONS_SHARE_SUCCESS';
export const ON_FETCH_ACTIONS_SHARE_FAILURE = 'ON_FETCH_ACTIONS_SHARE_FAILURE';
export const ON_FETCH_NO_VIOLATION_FRAUD_LOADING = 'ON_FETCH_NO_VIOLATION_FRAUD_LOADING';
export const ON_FETCH_NO_VIOLATION_FRAUD_SUCCESS = 'ON_FETCH_NO_VIOLATION_FRAUD_SUCCESS';
export const ON_FETCH_NO_VIOLATION_FRAUD_FAILURE = 'ON_FETCH_NO_VIOLATION_FRAUD_FAILURE';
export const ON_FETCH_RULE_CATEGORY_TREND_LOADING = 'ON_FETCH_RULE_CATEGORY_TREND_LOADING';
export const ON_FETCH_RULE_CATEGORY_TREND_SUCCESS = 'ON_FETCH_RULE_CATEGORY_TREND_SUCCESS';
export const ON_FETCH_RULE_CATEGORY_TREND_FAILURE = 'ON_FETCH_RULE_CATEGORY_TREND_FAILURE';
export const ON_FETCH_HIGH_ALERT_CUSTOMERS_LOADING = 'ON_FETCH_HIGH_ALERT_CUSTOMERS_LOADING';
export const ON_FETCH_HIGH_ALERT_CUSTOMERS_SUCCESS = 'ON_FETCH_HIGH_ALERT_CUSTOMERS_SUCCESS';
export const ON_FETCH_HIGH_ALERT_CUSTOMERS_FAILURE = 'ON_FETCH_HIGH_ALERT_CUSTOMERS_FAILURE';

//Rule Dashboard
export const ON_FETCH_RULE_STATS_LOADING = 'ON_FETCH_RULE_STATS_LOADING';
export const ON_FETCH_RULE_STATS_SUCCESS = 'ON_FETCH_RULE_STATS_SUCCESS';
export const ON_FETCH_RULE_STATS_FAILURE = 'ON_FETCH_RULE_STATS_FAILURE';
export const ON_FETCH_RULE_EFFICACY_LOADING = 'ON_FETCH_RULE_EFFICACY_LOADING';
export const ON_FETCH_RULE_EFFICACY_SUCCESS = 'ON_FETCH_RULE_EFFICACY_SUCCESS';
export const ON_FETCH_RULE_EFFICACY_FAILURE = 'ON_FETCH_RULE_EFFICACY_FAILURE';
export const ON_FETCH_RULE_BEHAVIOUR_LOADING = 'ON_FETCH_RULE_BEHAVIOUR_LOADING';
export const ON_FETCH_RULE_BEHAVIOUR_SUCCESS = 'ON_FETCH_RULE_BEHAVIOUR_SUCCESS';
export const ON_FETCH_RULE_BEHAVIOUR_FAILURE = 'ON_FETCH_RULE_BEHAVIOUR_FAILURE';
export const ON_FETCH_RULE_EFFICIENCY_LOADING = 'ON_FETCH_RULE_EFFICIENCY_LOADING';
export const ON_FETCH_RULE_EFFICIENCY_SUCCESS = 'ON_FETCH_RULE_EFFICIENCY_SUCCESS';
export const ON_FETCH_RULE_EFFICIENCY_FAILURE = 'ON_FETCH_RULE_EFFICIENCY_FAILURE';
export const ON_FETCH_RULE_EFFECTIVENESS_LOADING = 'ON_FETCH_RULE_EFFECTIVENESS_LOADING';
export const ON_FETCH_RULE_EFFECTIVENESS_SUCCESS = 'ON_FETCH_RULE_EFFECTIVENESS_SUCCESS';
export const ON_FETCH_RULE_EFFECTIVENESS_FAILURE = 'ON_FETCH_RULE_EFFECTIVENESS_FAILURE';
export const ON_FETCH_RULE_FEEDBACK_LOADING = 'ON_FETCH_RULE_FEEDBACK_LOADING';
export const ON_FETCH_RULE_FEEDBACK_SUCCESS = 'ON_FETCH_RULE_FEEDBACK_SUCCESS';
export const ON_FETCH_RULE_FEEDBACK_FAILURE = 'ON_FETCH_RULE_FEEDBACK_FAILURE';
export const ON_FETCH_RULE_FEEDBACK_ANALYSIS_LOADING = 'ON_FETCH_RULE_FEEDBACK_ANALYSIS_LOADING';
export const ON_FETCH_RULE_FEEDBACK_ANALYSIS_SUCCESS = 'ON_FETCH_RULE_FEEDBACK_ANALYSIS_SUCCESS';
export const ON_FETCH_RULE_FEEDBACK_ANALYSIS_FAILURE = 'ON_FETCH_RULE_FEEDBACK_ANALYSIS_FAILURE';
export const ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_LOADING =
  'ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_LOADING';
export const ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_SUCCESS =
  'ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_SUCCESS';
export const ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_FAILURE =
  'ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_FAILURE';

//One View
export const ON_REVIEWER_PARK_CASE_SUCCESS = 'ON_REVIEWER_PARK_CASE_SUCCESS';
export const ON_FETCH_REVIEWER_CASE_LOADING = 'ON_FETCH_REVIEWER_CASE_LOADING';
export const ON_FETCH_REVIEWER_CASE_SUCCESS = 'ON_FETCH_REVIEWER_CASE_SUCCESS';
export const ON_FETCH_REVIEWER_CASE_FAILURE = 'ON_FETCH_REVIEWER_CASE_FAILURE';
export const ON_REVIEWER_CASE_CLOSE_SUCCESS = 'ON_REVIEWER_CASE_CLOSE_SUCCESS';
export const ON_REVIEWER_ADD_TO_CASE_SUCCESS = 'ON_REVIEWER_ADD_TO_CASE_SUCCESS';
export const ON_REVIEWER_CASE_NOTIFICATION_SUCCESS = 'ON_REVIEWER_CASE_NOTIFICATION_SUCCESS';

//Compliance dashboard
export const ON_FETCH_FMR_REPORTED_CASE_LOADING = 'ON_FETCH_FMR_REPORTED_CASE_LOADING';
export const ON_FETCH_FMR_REPORTED_CASE_SUCCESS = 'ON_FETCH_FMR_REPORTED_CASE_SUCCESS';
export const ON_FETCH_FMR_REPORTED_CASE_FAILURE = 'ON_FETCH_FMR_REPORTED_CASE_FAILURE';

//Customer Communication
export const ON_FETCH_CALL_DISPOSITION_LIST_LOADING = 'ON_FETCH_CALL_DISPOSITION_LIST_LOADING';
export const ON_FETCH_CALL_DISPOSITION_LIST_SUCCESS = 'ON_FETCH_CALL_DISPOSITION_LIST_SUCCESS';
export const ON_FETCH_CALL_DISPOSITION_LIST_FAILURE = 'ON_FETCH_CALL_DISPOSITION_LIST_FAILURE';
export const ON_CUSTOMER_CALL_PLACED_SUCCESS = 'ON_CUSTOMER_CALL_PLACED_SUCCESS';
export const ON_CUSTOMER_CALL_END_SUCCESS = 'ON_CUSTOMER_CALL_END_SUCCESS';
export const ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_LOADING =
  'ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_LOADING';
export const ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_SUCCESS =
  'ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_SUCCESS';
export const ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_FAILURE =
  'ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_FAILURE';

//Notifications
export const ON_FETCH_NOTIFICATIONS_LIST_LOADING = 'ON_FETCH_NOTIFICATIONS_LIST_LOADING';
export const ON_FETCH_NOTIFICATIONS_LIST_SUCCESS = 'ON_FETCH_NOTIFICATIONS_LIST_SUCCESS';
export const ON_FETCH_NOTIFICATIONS_LIST_FAILURE = 'ON_FETCH_NOTIFICATIONS_LIST_FAILURE';
//Customer Events
export const ON_FETCH_CUSTOMER_EVENTS_LOADING = 'ON_FETCH_CUSTOMER_EVENTS_LOADING';
export const ON_FETCH_CUSTOMER_EVENTS_SUCCESS = 'ON_FETCH_CUSTOMER_EVENTS_SUCCESS';
export const ON_FETCH_CUSTOMER_EVENTS_FAILURE = 'ON_FETCH_CUSTOMER_EVENTS_FAILURE';
