import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchMoneyMuleReportsList } from 'actions/moneyMuleReportActions';
import MoneyMuleReportList from 'components/dashboards/MoneyMuleReportList';

const mapStateToProps = (state) => {
  return {
    userRole: state.auth.userCreds.roles,
    data: state.moneyMuleReport.allReports,
    hasProvisionalFields: state.user.configurations.provisionalFields
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchCases: bindActionCreators(onFetchMoneyMuleReportsList, dispatch)
  };
};

const MoneyMuleReportListContainer = connect(mapStateToProps, mapDispatchToProps)(MoneyMuleReportList);

export default MoneyMuleReportListContainer;
