import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as transactionHistorySearchActions from 'actions/transactionHistorySearchActions';
import { onSelectCase, onAddChildTxnToCase } from 'actions/caseReviewActions';
import IndepthMoneyMule from 'components/investigation/IndepthMoneyMule';

const mapStateToProps = (state) => {
  return {
    role: state.auth.userCreds.roles,
    investigationData: state.investigation,
    txnDetails: state.transactionDetails,
    selectedCase: state.caseAssignment.selectedCase,
    userName: state.auth.userCreds.userName
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    transactionHistorySearchActions: bindActionCreators(transactionHistorySearchActions, dispatch),
    addChildTxn: bindActionCreators(onAddChildTxnToCase, dispatch),
    selectCase: bindActionCreators(onSelectCase, dispatch)
  };
};

const IndepthMoneyMuleContainer = connect(mapStateToProps, mapDispatchToProps)(IndepthMoneyMule);

export default IndepthMoneyMuleContainer;
