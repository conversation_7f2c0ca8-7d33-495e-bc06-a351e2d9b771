import { each, isEmpty } from 'lodash';

export function inputChangeDataHandler(event, currentConfig, setStateFunction) {
  const { name, value } = event.target;
  const activationVal = value === 'disabled' ? 'enabled' : 'disabled';
  const tempConfig = {
    ...currentConfig,
    [name]: name === 'activation' ? activationVal : value,
    isEdited: true
  };

  setStateFunction(tempConfig);
}

export function saveConfigurationsDataHandler(
  event,
  configurationsData,
  currentConfig,
  setStateFunction,
  saveConfigurations
) {
  event.preventDefault();
  setStateFunction((prevState) => ({
    ...prevState,
    isEdited: false
  }));

  let formData = {
    configType: configurationsData.configType.toString(),
    updateConfigPoints: {}
  };

  each(Object.keys(currentConfig), (currentConfItem) => {
    if (
      currentConfig[currentConfItem] !== configurationsData.configPoints[currentConfItem] &&
      currentConfItem !== 'isEdited'
    ) {
      formData.updateConfigPoints[currentConfItem] = currentConfig[currentConfItem].toString();
    }
  });

  if (!isEmpty(formData.updateConfigPoints)) saveConfigurations(formData);
}

export function resetConfigurationsDataHandler(configurationsData, setStateFunction) {
  const tempConfig = {
    ...configurationsData.configPoints,
    isEdited: false
  };

  setStateFunction(tempConfig);
}
