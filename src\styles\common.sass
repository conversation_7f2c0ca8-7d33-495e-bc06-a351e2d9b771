/* Div Styles: Start */
html
  box-sizing: border-box
  margin: 0
  height: 100%
  width: 100%
  overflow-wrap: break-word
  scroll-behavior: smooth

body, #app, .root-div
  min-height: 100%
  max-width: 100%
  margin: 0
  padding: 0
  font: $default-typo
  overflow-x: hidden

.main-wrapper, .content-wrapper
  margin: 0
  padding: 0
  height: 100%

.content-wrapper
  padding: 20px
  width: auto
  height: calc(100vh - 54px)
  overflow-y: auto

.content-placeholder
  height: 100%
  display: flex
  justify-content: center
  align-content: center
  align-items: center
  opacity: 0.65
  font: $title-typo

.hide
  display: none

.visibility-hide
  visibility: hidden !important

.row
  margin: 0

.no-margin
  margin: 0 !important

.no-padding
  padding: 0 !important

.no-right-padding
  padding-right: 0 !important

.no-break
  overflow-wrap: none !important

.cursor-pointer
  cursor: pointer

.search-box, .card-subtitle
  padding-top: 12px
  padding-bottom: 12px

.txn-info, .case-details
  margin: 12px


.fullscreen-div
  height: 100vh

.data-columns
  display: flex
  flex-direction: column
  p
    flex: 1 0 auto

.tranpsarent-overlay
  position: absolute
  width: 100%
  height: 100%
  background-color: transparent
  z-index: 12
  cursor: pointer

.no-data-div
  display: flex
  padding: 50px 0
  align-items: center
  justify-content: center
/* Div Styles: End */

/* Button Styles: Start */
.btn:not(.btn-sm):not(.btn-xs)
  min-width: 90px
  font: $link-typo
/* Button Styles: End */

/* Input Styles: Start */
input, select, textarea, .input-group-text
  padding: 0 5px !important
  font: $default-typo !important
  border-width:  1px
  border-style: solid

input:not([type='radio']):not([type='checkbox']):not([type='textarea']), select
  height: 30px !important

.disabled-form
  input:not([type='radio']):not([type='checkbox']), select, textarea
    &:disabled
      border: 0 !important
      background-image: none

/* Input Styles: End */
.card
  .badge
    padding: 6px 12px
    font: $title-typo

dl, ol, ul, p
  padding-left: 0
  margin-bottom: 0

li
  list-style-type: none

a, a:visited, a:focus
  outline: none

//scrollbar
::-webkit-scrollbar
  width: 5px
  height: 5px

::-webkit-scrollbar-thumb
  border-radius: 10px

.btn:focus, .show > .btn-outline-primary.dropdown-toggle:focus, span:focus, button:focus,
div:focus, i:focus
  -webkit-box-shadow: none !important
  box-shadow: none !important
  outline: none !important

.btn.disabled
  cursor: not-allowed !important

.scp-container table
  th
    font: $title-typo
  .row
    margin-bottom: 10px

.dropdown-menu
  border: none
  max-height: 400px
  overflow-y: auto
  z-index: 2

.no-data-card-padding
  padding: 120px 0 !important

.rule-sub-table
  padding: 0 30px 5px 30px

.watchlist-link, .release-fund-view-link
  margin-left: 10px

.rule-check
  padding-left: 20px

.assignment-action
  margin-right: 10px

.align-middle
  vertical-align: middle

.block
  display: block

.none
  display: none

.form-check-input
  margin-top: 2px

h3
  font: $h3-typo

.form-control, .table-sm th, .btn-sm, button
  font: $label-typo

label
  font: $label-typo

small
  font: $small-typo

.bulk-closure-btn
  margin-bottom: 0.75rem

.required-star
  font: $small-typo
  position: relative
  left: 4px
  top: -6px

.rmsc
  --rmsc-p: 4px
  --rmsc-radius: 0.25rem
  --rmsc-h: 30px

.download-button-link
  text-decoration: none
  color: inherit
  &.disabled
    pointer-events: none
  &:hover
    text-decoration: none
    color: inherit

.conditions-list
  list-style-type: disc
  margin-left: 1em

.conditions-table
  margin: 40px 0 1rem 0

.add-condition-btn
  margin-top: 28px

.dynamic-counter-modal
  max-width: 700px !important
  .form-check
    margin-bottom: 2rem

.create-counter-btn
  margin-top: 1rem

.title-dropdown
  .badge
    font: $default-typo !important

.dropdown-card-title
  font: $h3-typo !important

.txn-info > .detail-cols
  gap: 40px

.case-details > .detail-cols
  gap: 40px

.channel-check
  margin-left: 20px

.channel-select
  width: 12%

.collapsible-pill-container
  margin: 12px 12px

.similar-category-multi-select.not-clearable
  .clear-selected-button
    display: none

.tooltip-link
  text-decoration: underline dotted 3px

.editableDiv
  font: $default-typo !important
  &:empty:before
    content: attr(placeholder)
    font: $small-typo

.rmsc
  .dropdown-container
    height: 30px !important
    border-radius: 0.375rem

.open-link-btn
  border: none
  cursor: pointer
  display: inline-block

.vertical-tab
  ul
    width: 100%

.prefilter-search
  .select-box
    width: 15%
  .input-box
    margin: 0 10px

.vertical-tab
  ul
    width: 100%

.disable-btn
  span
    pointer-events: none
    cursor: not-allowed !important

.rule-upload-btn
  font-size: 16px !important
  line-height: 1.2 !important
  font-weight: 500 !important

.download-csv-btn
  padding: 0
  a
    display: block
    padding:0.3rem 0.5rem
    text-decoration: none
    border-radius: 3px
    &:hover
      color: white !important

.list-icon
  font-size: 24px

.rule-name
  font: $title-typo

.feedback-buttons
  &:nth-child(odd)
    border-right: 1px solid
  button
    border-radius: 50%
    padding: 8px
    margin-left: 8px
    line-height: 1

.condition-name
  font-size: 15px

.disabled
  cursor: not-allowed
  opacity: 0.5

.settings-btn
  padding: 0
  width: 100%
  text-align: left
  border: none !important

.transaction-grid
  column-width: auto
  column-gap: 10px
  column-count: 4

  .transaction-item
    break-inside: avoid-column;
    padding: 1rem

  @media (max-width: 1200px)
    column-count: 3

  @media (max-width: 900px)
    column-count: 2

  @media (max-width: 600px)
    column-count: 1

.badge-sm
  font-size: 0.75rem !important

.border-left
  position: relative !important
  border-left: none !important

  &:after
    content: '' !important
    position: absolute !important
    left: -8px !important
    top: 10% !important
    bottom: 10% !important
    width: 1px !important
    background-color: #d3d0d0 !important

  @media (max-width: 991px)
    &:after
      display: none !important

.risk-lvel-badge
  height: 100px
  width: 100px
  opacity: 0.75



