import _ from 'lodash';
import React, { Suspense, lazy, useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { useHistory, useParams } from 'react-router-dom';
import { Row, Col, TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import Loader from 'components/loader/Loader';
import CaseDetailCard from 'containers/common/CaseDetailCardContainer';
import TransactionDetailCardContainer from 'containers/common/TransactionDetailCardContainer';
import InvestigationActionsContainer from 'containers/investigation/InvestigationActionsContainer';
import CPFIRFormContainer from 'containers/incidents/CPIFRFormContainer';
import { isCooperative } from 'constants/publicKey';
import RuleFeedbackFormContainer from 'containers/common/RuleFeedbackFormContainer';
import TransactionRiskScoreInfo from '../common/TransactionRiskScoreInfo';
import CaseNotificationContainer from 'containers/notifications/CaseNotificationContainer';

const AgentInfoCardContainer = lazy(() => import('containers/common/AgentInfoCardContainer'));
const CustomerInfoCardContainer = lazy(() => import('containers/common/CustomerInfoCardContainer'));
const MerchantInfoCardContainer = lazy(() => import('containers/common/MerchantInfoCardContainer'));
const Log = lazy(() => import('containers/common/LogContainer'));
const NotationContainer = lazy(() => import('containers/common/NotationContainer'));
const VerdictModalContainer = lazy(() => import('containers/common/VerdictModalContainer'));
const HistoryTxnTableContainer = lazy(() => import('containers/common/HistoryTxnTableContainer'));
const ViolatedRulesCardContainer = lazy(
  () => import('containers/common/ViolatedRulesCardContainer')
);
const SimilarTxnsTableContainer = lazy(
  () => import('containers/investigation/SimilarTxnsTableContainer')
);
const PastInvestigationCardContainer = lazy(
  () => import('containers/common/PastInvestigationCardContainer')
);
const TransactionTimeAmountTrendContainer = lazy(
  () => import('containers/common/TransactionTimeAmountTrendContainer')
);
const CPIFRDownloadHistoryContainer = lazy(
  () => import('containers/incidents/CPIFRDownloadHistoryContainer')
);
const CustomerEventsTableContainer = lazy(
  () => import('containers/investigation/CustomerEventsTableContainer')
);

const Indepth = ({
  role,
  txnDetails,
  selectedCase,
  transactionHistorySearchActions,
  documentStatus,
  selectCase,
  fetchRulesWithConditions
}) => {
  let tabList = ['Details', 'Trend', 'History', 'Similar'];
  const history = useHistory();
  const { txnId, channel } = useParams();
  const tabRef = useRef(null);
  const [renderedTab, setRenderedTab] = useState([]);

  useEffect(() => {
    txnId && selectCase({ txnId, channel: 'frm' });
    tabRef?.current?.changeTab(0);
  }, [txnId]);

  useEffect(() => {
    if (!_.isEmpty(selectedCase)) {
      document.title = 'BANKiQ FRC | Investigation - ' + selectedCase.caseRefNo;
      transactionHistorySearchActions.onClearSearch();
    }

    const formData = {
      rulesCodes: txnDetails?.details?.reViolatedRules || selectedCase?.reViolatedRules
    };

    !_.isEmpty(channel) &&
      !_.isEmpty(txnDetails?.details?.reViolatedRules || selectedCase?.reViolatedRules) &&
      fetchRulesWithConditions(formData, channel);

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, [selectedCase]);

  const tabChangeAction = (tabId) => {
    if (!_.includes(renderedTab, tabId)) setRenderedTab((prev) => [...prev, tabId]);
  };

  const entityId = _.has(txnDetails.details, 'entityId') ? txnDetails.details.entityId.value : '';

  selectedCase.currentStatus !== 'READY_TO_OPEN' && tabList.push('Logs');

  const cognitiveResponse = !_.isEmpty(txnDetails.details.cognitiveResponse)
    ? JSON.parse(txnDetails.details.cognitiveResponse)
    : {};
  const cognitiveViolations = _.isEmpty(cognitiveResponse.unusualMethods)
    ? []
    : cognitiveResponse.unusualMethods;

  const investigationAction = <InvestigationActionsContainer history={history} channel="frm" />;
  return (
    <div className={'content-wrapper'}>
      <Tabs
        pills
        ref={tabRef}
        tabNames={tabList}
        action={investigationAction}
        tabChangeAction={tabChangeAction}>
        <TabPane tabId={0}>
          <Row className="ms-0">
            <Col md="12" sm="12">
            <Row className="ms-0">
                {selectedCase.currentStatus !== 'READY_TO_OPEN' ? (
                  <Col
                    lg={selectedCase.currentStatus !== 'READY_TO_OPEN' ? 7 : 12}
                   >
                    {isCooperative && <CaseNotificationContainer txnId={txnId} channel="frm" />}
                    {selectedCase.currentStatus !== 'READY_TO_OPEN' && (
                      <CaseDetailCard
                        caseDetails={selectedCase}
                        documentStatus={documentStatus}
                        channel="frm"
                      />
                    )}
                  </Col>
                ) : null}

                {!_.isEmpty(txnDetails?.details?.riskDetails) && (
                  <Col
                    lg={selectedCase.currentStatus !== 'READY_TO_OPEN' ? 5 : 12}
                    >
                    <TransactionRiskScoreInfo details={txnDetails?.details} />
                  </Col>
                )}
              </Row>
              <TransactionDetailCardContainer channel="frm" />
              <Suspense fallback={<Loader show={true} />}>
                {_.lowerCase(txnDetails.details.entityCategory) === 'merchant' ? (
                  <MerchantInfoCardContainer merchantId={entityId} channel="frm" />
                ) : _.lowerCase(txnDetails.details.entityCategory) === 'customer' ? (
                  <CustomerInfoCardContainer customerId={entityId} channel="frm" />
                ) : _.lowerCase(txnDetails.details.entityCategory) === 'agent' ? (
                  <AgentInfoCardContainer agentId={entityId} channel="frm" />
                ) : null}
              </Suspense>
            </Col>

            <Col xl={_.has(selectedCase, 'caseId') ? '8' : '12'} xs="12">
              <Suspense fallback={<Loader show={true} />}>
                {!_.isEmpty(txnDetails.details) && (
                  <ViolatedRulesCardContainer
                    transactionId={txnId}
                    txnTimestamp={txnDetails?.details?.transactionInfo?.txnTimestamp}
                    reViolatedRules={
                      txnDetails?.details?.reViolatedRules || selectedCase?.reViolatedRules || []
                    }
                    cognitiveViolations={cognitiveViolations}
                    channel="frm"
                  />
                )}
                <PastInvestigationCardContainer
                  role={role}
                  transactionId={txnId}
                  entityId={entityId}
                  channel="frm"
                  contextKey="pastInvestigationIndepth"
                />
              </Suspense>
            </Col>

            {_.has(selectedCase, 'caseId') && (
              <Col xl="4" xs="12">
                <Suspense fallback={<Loader show={true} />}>
                  <NotationContainer
                    caseId={selectedCase.caseId}
                    caseRefNo={selectedCase.caseRefNo}
                    disableComment={selectedCase.currentStatus == 'READY_TO_OPEN'}
                    channel="frm"
                  />
                </Suspense>
              </Col>
            )}
          </Row>
        </TabPane>

        <TabPane tabId={1}>
          {!_.isEmpty(selectedCase) && _.includes(renderedTab, 1) && (
            <Suspense fallback={<Loader show={true} />}>
              <TransactionTimeAmountTrendContainer
                txnId={txnId}
                entityId={entityId}
                txnAmount={selectedCase?.txnAmount}
                txnTimestamp={selectedCase?.txnTimestamp}
              />
            </Suspense>
          )}
        </TabPane>

        <TabPane tabId={2}>
          {!_.isEmpty(selectedCase) && _.includes(renderedTab, 2) && (
            <div>
              <Suspense fallback={<Loader show={true} />} className="mb-3">
                <CustomerEventsTableContainer customerId={entityId} />
              </Suspense>
              <Suspense fallback={<Loader show={true} />}>
                <HistoryTxnTableContainer
                  entityId={entityId}
                  entityCategory={txnDetails.details.entityCategory || ''}
                  channel={'frm'}
                  txnDetails={txnDetails.details}
                  showSearchFilter={true}
                />
              </Suspense>
            </div>
          )}
        </TabPane>

        <TabPane tabId={3}>
          {!_.isEmpty(selectedCase) && _.includes(renderedTab, 3) && (
            <Suspense fallback={<Loader show={true} />}>
              <SimilarTxnsTableContainer
                txnId={txnId}
                txnDetails={txnDetails.details}
                channel="frm"
              />
            </Suspense>
          )}
        </TabPane>

        <TabPane tabId={4}>
          {!_.isEmpty(selectedCase) && _.includes(renderedTab, 4) && (
            <Suspense fallback={<Loader show={true} />}>
              <Log module="Case" id={selectedCase.caseRefNo || ''} />
              {selectedCase?.internalId && (
                <CPIFRDownloadHistoryContainer internalId={selectedCase?.internalId} />
              )}
            </Suspense>
          )}
        </TabPane>
      </Tabs>

      <Suspense fallback={<Loader show={true} />}>
        <VerdictModalContainer
          caseId={selectedCase.caseRefNo}
          channel={'frm'}
          caseDetails={selectedCase}
          bulkCaseIds={
            isCooperative &&
            !_.isEmpty(selectedCase.investigationVerdict) &&
            _.isEmpty(selectedCase.investigatorVerdict) &&
            _.toLower(selectedCase.currentStage) == 'reviewer' && [selectedCase.caseRefNo]
          }
          partnerId={selectedCase.partnerId}
          violatedRules={txnDetails.details?.reViolatedRules}
          bucket={
            isCooperative &&
            !_.isEmpty(selectedCase.investigationVerdict) &&
            _.isEmpty(selectedCase.investigatorVerdict) &&
            _.toLower(selectedCase.currentStage) == 'reviewer' &&
            'Unverified'
          }
        />
      </Suspense>

      <CPFIRFormContainer allowClose={true} />
      {!_.isEmpty(selectedCase?.reViolatedRules) && (
        <RuleFeedbackFormContainer rulesCodes={selectedCase?.reViolatedRules} />
      )}
    </div>
  );
};

Indepth.propTypes = {
  role: PropTypes.string.isRequired,
  txnDetails: PropTypes.object.isRequired,
  selectedCase: PropTypes.object.isRequired,
  transactionHistorySearchActions: PropTypes.object.isRequired,
  documentStatus: PropTypes.object.isRequired,
  selectCase: PropTypes.func.isRequired,
  fetchRulesWithConditions: PropTypes.func.isRequired
};

export default Indepth;
