import React from 'react';
import { groupBy, map } from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import NotificationList from './NotificationList';
import { Label, ListGroup, ListGroupItem, Row, Col } from 'reactstrap';

function NotificationManager({ notifications }) {
  const dateWiseNotifications = () =>
    groupBy(notifications.list, (d) => moment(d['timestamp']).format('YYYY-MM-DD'));

  const getDateLabel = (date) => {
    const dateDiff = moment(date).diff(moment(), 'day');
    return dateDiff === 0 ? 'Today' : dateDiff === -1 ? 'Yesterday' : moment(date).format('ll');
  };

  if (notifications.list.length === 0) {
    return <div className="no-data-div">No notifications found</div>;
  }

  return (
    <>
      <ListGroup className="notification-header-list mb-2">
        <ListGroupItem>
          <Row>
            <Col md="1"> </Col>
            <Col md="2">Customer ID</Col>
            <Col md="3">Message</Col>
            <Col md="2">Time</Col>
            <Col md="2">Acknowledged By</Col>
            <Col md="2">Acknowledged On</Col>
          </Row>
        </ListGroupItem>
      </ListGroup>
      {map(dateWiseNotifications(), (datedNotifications, key) => (
        <div key={key} className="mb-3">
          <Label>{getDateLabel(key)}</Label>
          <NotificationList list={datedNotifications} />
        </div>
      ))}
    </>
  );
}

NotificationManager.propTypes = {
  notifications: PropTypes.object.isRequired
};

export default NotificationManager;
