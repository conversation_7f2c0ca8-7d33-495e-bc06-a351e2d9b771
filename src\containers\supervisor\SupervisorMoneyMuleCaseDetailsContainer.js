import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as transactionHistorySearchActions from 'actions/transactionHistorySearchActions';
import { onSelectCase } from 'actions/caseReviewActions';
import SupervisorMoneyMuleCaseDetails from 'components/supervisor/SupervisorMoneyMuleCaseDetails';

const mapStateToProps = (state) => {
  return {
    role: state.auth.userCreds.roles,
    userName: state.auth.userCreds.userName,
    txnDetails: state.transactionDetails,
    selectedCase: state.caseAssignment.selectedCase
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    transactionHistorySearchActions: bindActionCreators(transactionHistorySearchActions, dispatch),
    selectCase: bindActionCreators(onSelectCase, dispatch)
  };
};

const SupervisorMoneyMuleCaseDetailsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(SupervisorMoneyMuleCaseDetails);

export default SupervisorMoneyMuleCaseDetailsContainer;
