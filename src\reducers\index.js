/* eslint-disable import/namespace */
import { combineReducers } from 'redux';
import storage from 'redux-persist/lib/storage';
import initialState from './initialState';
import auth from './authReducer';
import user from './userReducer';
import partnerBanks from './partnerBanksReducer';
import alert from './alertReducer';
import toggle from './toggleReducer';
import logs from './logsReducer';
import violatedRules from './violatedRulesReducer';
import monitor from './monitoringReducer';
import caseAssignment from './caseAssignmentReducer';
import notations from './notationsReducer';
import investigation from './investigationReducer';
import ruleCreation from './ruleCreationReducer';
import dynamicCounters from './dynamicCountersReducer';
import ruleConfigurator from './ruleConfiguratorReducer';
import transactionDetails from './transactionDetailsReducer';
import prefilter from './prefilterReducer';
import scp from './scpReducer';
import cognitiveStatus from './cognitiveStatusReducer';
import releaseFunds from './releaseFundsReducer';
import prefiltersList from './prefiltersListReducer';
import uds from './udsReducer';
import profiling from './profilingReducer';
import demographicDetails from './demographicDetailsReducer';
import statisticsDetails from './statisticsDetailsReducer';
import advanceSearchTxns from './advanceSearchTxnsReducer';
import sandboxing from './sandboxingReducer';
import snoozeRules from './snoozeRulesReducer';
import citations from './citationsReducer';
import strReport from './strReportReducer';
import moneyMuleReport from './moneyMuleReportReducer';
import settings from './settingsReducer';
import caseDocument from './caseDocumentReducer';
import rfiReports from './rfiReportsReducer';
import incidents from './incidentsReducer';
import slaDashboard from './slaDashboardReducer';
import businessDashboard from './businessDashboardReducer';
import ruleDashboard from './ruleDashboardReducer';
import oneView from './oneViewReducer';
import complianceDashboard from './complianceDashboardReducer';
import customerCommunication from './CustomerCommunicationReducer';
import notifications from './notificationsReducer';
import customerEvents from './customerEventsReducer';
import { ON_SUCCESSFUL_LOGOUT } from 'constants/actionTypes';

const appReducer = combineReducers({
  auth,
  user,
  partnerBanks,
  alert,
  toggle,
  logs,
  violatedRules,
  monitor,
  caseAssignment,
  notations,
  investigation,
  ruleCreation,
  dynamicCounters,
  ruleConfigurator,
  transactionDetails,
  prefilter,
  scp,
  cognitiveStatus,
  releaseFunds,
  prefiltersList,
  uds,
  profiling,
  demographicDetails,
  statisticsDetails,
  advanceSearchTxns,
  sandboxing,
  snoozeRules,
  citations,
  strReport,
  moneyMuleReport,
  settings,
  rfiReports,
  caseDocument,
  incidents,
  slaDashboard,
  businessDashboard,
  ruleDashboard,
  oneView,
  complianceDashboard,
  customerCommunication,
  notifications,
  customerEvents
});

const rootReducer = (state, action) => {
  if (action && action.type === ON_SUCCESSFUL_LOGOUT) {
    Object.keys(state).forEach((key) => {
      storage.removeItem(`persist:${key}`);
    });
    state = initialState;
  }
  return appReducer(state, action);
};

export default rootReducer;
