import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { FormGroup, Label, Input, Col } from 'reactstrap';
import ConfigFormWrapper from 'components/scp/ConfigFormWrapper';
import {
  inputChangeDataHandler,
  saveConfigurationsDataHandler,
  resetConfigurationsDataHandler
} from 'components/scp/scpFunctions';

function AuthMailer({ highlightText, saveConfigurations, configurationsData }) {
  const [authMailer, setAuthMailer] = useState({
    isEdited: false,
    ...configurationsData.configPoints
  });

  return (
    <ConfigFormWrapper
      configTitle="Auth Mailer"
      activationId="activationAuthMailer"
      highlightText={highlightText}
      data={authMailer}
      handleSaveConfigurations={(event) =>
        saveConfigurationsDataHandler(
          event,
          configurationsData,
          authMailer,
          setAuthMailer,
          saveConfigurations
        )
      }
      handleInputChange={(event) => inputChangeData<PERSON>and<PERSON>(event, authMailer, setAuthMailer)}
      handleResetConfigurations={() =>
        resetConfigurationsDataHandler(configurationsData, setAuthMailer)
      }>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="userName" className="searchable">
          {highlightText('User Name:')}
        </Label>
        <Col sm={4} md={3} lg={2} className="no-right-padding">
          <Input
            type="email"
            name="userName"
            id="userName"
            onChange={(event) => inputChangeDataHandler(event, authMailer, setAuthMailer)}
            max={24}
            min={0}
            value={authMailer.userName}
            required
            step="any"
          />
        </Col>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="password" className="searchable">
          {highlightText('password:')}
        </Label>
        <Col sm={4} md={3} lg={2} className="no-right-padding">
          <Input
            type="text"
            name="password"
            id="password"
            onChange={(event) => inputChangeDataHandler(event, authMailer, setAuthMailer)}
            value={authMailer.password}
            required
          />
        </Col>
      </FormGroup>
    </ConfigFormWrapper>
  );
}

AuthMailer.propTypes = {
  highlightText: PropTypes.func.isRequired,
  saveConfigurations: PropTypes.func.isRequired,
  configurationsData: PropTypes.object.isRequired
};

export default AuthMailer;
